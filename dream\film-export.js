// 底片冲洗功能 - 解决跨域问题的图片导出
document.addEventListener('DOMContentLoaded', function() {
  // 监听导出梦境底片按钮点击事件
  const exportDreamBtn = document.getElementById('exportDreamBtn');
  if (exportDreamBtn) {
    exportDreamBtn.addEventListener('click', function() {
      // 显示模态框
      document.getElementById('exportModal').classList.add('active');
      
      // 获取梦境数据
      const dreamDescription = document.getElementById('dreamDescription').value;
      const dreamAnalysis = document.getElementById('dreamAnalysisText').innerText;
      const dreamImage = document.getElementById('dreamImage');
      const dreamTime = document.getElementById('dreamTime').value;
      const dreamLocation = document.getElementById('dreamLocation').value;
      
      // 获取底片容器
      const dreamFilmContainer = document.getElementById('dreamFilmContainer');
      
      if (!dreamFilmContainer) {
        alert('无法找到梦境底片容器，请重试。');
        return;
      }
      
      // 创建底片DOM
      const filmElement = createDreamFilmDOM(dreamDescription, dreamAnalysis, dreamImage, dreamTime, dreamLocation);
      
      // 显示底片
      dreamFilmContainer.innerHTML = '';
      dreamFilmContainer.appendChild(filmElement);
      
      // 添加关闭模态框事件
      document.getElementById('closeExportModal').addEventListener('click', function() {
        document.getElementById('exportModal').classList.remove('active');
      });
      
      document.getElementById('closeExportModalBtn').addEventListener('click', function() {
        document.getElementById('exportModal').classList.remove('active');
      });
    });
  }
  
  // 监听下载按钮点击事件
  const downloadBtn = document.getElementById('downloadDreamFilm');
  if (downloadBtn) {
    downloadBtn.addEventListener('click', function(event) {
      // 阻止默认事件
      event.preventDefault();
      
      // 获取梦境数据
      const dreamDescription = document.getElementById('dreamDescription').value;
      const dreamAnalysis = document.getElementById('dreamAnalysisText').innerText;
      const dreamImage = document.getElementById('dreamImage');
      const dreamTime = document.getElementById('dreamTime').value;
      const dreamLocation = document.getElementById('dreamLocation').value;
      
      // 获取底片容器和模态框
      const dreamFilmContainer = document.getElementById('dreamFilmContainer');
      
      if (!dreamFilmContainer) {
        alert('无法找到梦境底片容器，请重试。');
        return;
      }
      
      // 添加加载提示
      dreamFilmContainer.innerHTML = '<div style="text-align: center; padding: 20px;">正在准备保存图片，请稍候...</div>';
      
      // 检查是否有图片
      if (dreamImage && dreamImage.src && dreamImage.src !== window.location.href) {
        // 先下载图片，然后再生成底片
        downloadImageLocally(dreamImage.src, function(localImageUrl) {
          // 创建底片DOM
          createDreamFilmWithLocalImage(dreamDescription, dreamAnalysis, localImageUrl, dreamTime, dreamLocation, function(filmElement) {
            // 显示底片并直接触发导出功能
            exportFilmAsImage(filmElement, dreamFilmContainer);
          });
        });
      } else {
        // 没有图片，直接生成底片
        const filmElement = createDreamFilmDOM(dreamDescription, dreamAnalysis, null, dreamTime, dreamLocation);
        exportFilmAsImage(filmElement, dreamFilmContainer);
      }
    });
  }
  
  // 下载图片到本地
  function downloadImageLocally(imageUrl, callback) {
    // 创建一个新的图片元素
    const img = new Image();
    img.crossOrigin = 'anonymous'; // 尝试解决跨域问题
    
    img.onload = function() {
      try {
        // 创建Canvas
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        
        // 绘制图片到Canvas
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        
        // 转换为Base64
        const base64 = canvas.toDataURL('image/png');
        callback(base64);
      } catch (e) {
        console.error('图片转换失败:', e);
        // 如果失败，返回null
        callback(null);
      }
    };
    
    img.onerror = function() {
      console.error('图片加载失败');
      callback(null);
    };
    
    // 添加随机参数避免缓存
    img.src = imageUrl + (imageUrl.includes('?') ? '&' : '?') + 'nocache=' + Math.random();
    
    // 如果图片已经加载完成，直接触发onload
    if (img.complete) {
      img.onload();
    }
    
    // 设置超时
    setTimeout(function() {
      if (!img.complete) {
        img.src = ''; // 取消加载
        callback(null);
      }
    }, 5000);
  }
  
  // 使用本地图片创建底片
  function createDreamFilmWithLocalImage(description, analysis, localImageUrl, time, location, callback) {
    // 创建底片DOM
    const filmElement = createDreamFilmDOM(description, analysis, null, time, location);
    
    // 如果有本地图片URL，添加到底片中
    if (localImageUrl) {
      const imageContainer = document.createElement('div');
      imageContainer.className = 'dream-film-image-container';
      imageContainer.style.textAlign = 'center';
      
      const image = document.createElement('img');
      image.className = 'dream-film-image';
      image.src = localImageUrl;
      image.alt = '梦境图片';
      image.style.maxWidth = '400px';
      image.style.borderRadius = '8px';
      image.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
      
      imageContainer.appendChild(image);
      
      // 插入到内容区域的适当位置
      const content = filmElement.querySelector('.dream-film-content');
      if (content && content.firstChild) {
        content.insertBefore(imageContainer, content.firstChild.nextSibling);
      } else if (content) {
        content.appendChild(imageContainer);
      }
    }
    
    callback(filmElement);
  }
  
  // 动态加载脚本的工具函数
  function loadScript(src) {
    return new Promise((resolve, reject) => {
      console.log('开始加载脚本:', src);
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        console.log('脚本加载成功:', src);
        resolve();
      };
      script.onerror = (e) => {
        console.error('脚本加载失败:', src, e);
        reject(new Error('无法加载脚本: ' + src));
      };
      document.head.appendChild(script);
      
      // 设置超时
      setTimeout(() => {
        if (!script.loaded) {
          console.error('脚本加载超时:', src);
          reject(new Error('脚本加载超时: ' + src));
        }
      }, 10000); // 10秒超时
    });
  }

  // 显示底片并添加下载按钮
  function showFilmWithDownloadButton(filmElement, container) {
    // 清空容器
    container.innerHTML = '';
    
    // 添加底片
    container.appendChild(filmElement);
    
    // 隐藏原始按钮
    const actionsContainer = document.querySelector('.dream-export-actions');
    if (actionsContainer) {
      actionsContainer.style.display = 'none';
    }
    
    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.textAlign = 'center';
    buttonContainer.style.marginTop = '20px';
    
    // 添加图片导出按钮
    const exportImageBtn = document.createElement('button');
    exportImageBtn.className = 'secondary-button';
    exportImageBtn.innerHTML = '<i class="fas fa-image"></i> 保存为图片';
    exportImageBtn.style.margin = '10px';
    exportImageBtn.style.display = 'inline-block';
    
    exportImageBtn.addEventListener('click', async function() {
      const loadingText = document.createElement('div');
      loadingText.textContent = '正在生成图片，请稍候...';
      loadingText.style.textAlign = 'center';
      loadingText.style.padding = '10px';
      container.appendChild(loadingText);
      
      // 检查filmElement是否存在
      if (!filmElement) {
        console.error('错误: filmElement不存在');
        alert('生成图片失败: 找不到梦境内容元素');
        return;
      }
      
      // 保存原始样式
      const originalStyle = {
        height: filmElement.style.height,
        maxHeight: filmElement.style.maxHeight,
        overflow: filmElement.style.overflow
      };
      
      try {
        // 隐藏按钮容器，避免它被截入图片
        buttonContainer.style.display = 'none';
        
        // 加载html2canvas库
        try {
          if (typeof html2canvas === 'undefined') {
            loadingText.textContent = '正在准备图片导出...';
            
            // 内置简化版截图工具
            window.html2canvas = function(element, options) {
              return new Promise((resolve, reject) => {
                try {
                  console.log('使用内置截图工具...');
                  
                  // 获取DOM元素内容
                  const title = element.querySelector('.dream-film-title')?.textContent || '冲洗梦的胶片';
                  const date = element.querySelector('.dream-film-date')?.textContent || '';
                  const description = element.querySelector('.dream-film-description')?.textContent || '';
                  const analysis = element.querySelector('.dream-film-analysis')?.textContent || '';
                  
                  // 获取文字风格设置
                  const textStyle = document.getElementById('textStyleSelect') ? 
                                   document.getElementById('textStyleSelect').value : 'normal';
                  const isHandwritten = textStyle === 'handwritten';
                  
                  // 尝试获取图片
                  const imgElement = element.querySelector('.dream-film-image');
                  
                  // 设置画布尺寸
                  const width = options.width || element.offsetWidth;
                  const height = options.height || element.offsetHeight;
                  const scale = options.scale || 2;
                  
                  // 创建画布
                  const canvas = document.createElement('canvas');
                  canvas.width = width * scale;
                  canvas.height = height * scale;
                  
                  const ctx = canvas.getContext('2d');
                  ctx.scale(scale, scale);
                  
                  // 绘制背景
                  ctx.fillStyle = options.backgroundColor || '#e6f2ff';
                  ctx.fillRect(0, 0, width, height);
                  
                  // 绘制边框
                  ctx.strokeStyle = '#ddd';
                  ctx.lineWidth = 2;
                  ctx.strokeRect(10, 10, width - 20, height - 20);
                  
                  // 绘制标题
                  ctx.fillStyle = '#333';
                  // 根据是否手写设置不同字体
                  if (isHandwritten) {
                    ctx.font = 'bold 28px "LocalHandwritten", cursive';
                  } else {
                    ctx.font = 'bold 28px "NanoQyongDaSong C", serif';
                  }
                  ctx.textAlign = 'center';
                  ctx.fillText(title, width/2, 50);
                  
                  // 绘制日期
                  ctx.fillStyle = '#666';
                  // 根据是否手写设置不同字体
                  if (isHandwritten) {
                    ctx.font = '14px "LocalHandwritten", cursive';
                  } else {
                    ctx.font = '14px "NanoQyongDaSong C", Arial';
                  }
                  ctx.fillText(date, width/2, 80);
                  
                  // 计算内容区域
                  let currentY = 120;
                  
                  // 绘制描述标题
                  ctx.fillStyle = '#333';
                  // 根据是否手写设置不同字体
                  if (isHandwritten) {
                    ctx.font = 'bold 18px "LocalHandwritten", cursive';
                  } else {
                    ctx.font = 'bold 18px "NanoQyongDaSong C", Arial';
                  }
                  ctx.textAlign = 'left';
                  ctx.fillText('梦境描述:', 30, currentY);
                  currentY += 30;
                  
                  // 绘制描述内容
                  ctx.fillStyle = '#333';
                  // 根据是否手写设置不同字体
                  if (isHandwritten) {
                    ctx.font = '16px "LocalHandwritten", cursive';
                  } else {
                    ctx.font = '16px "NanoQyongDaSong C", Arial';
                  }
                  
                  // 文本换行函数
                  function drawWrappedText(text, x, y, maxWidth, lineHeight) {
                    if (!text) return 0;
                    
                    const words = text.split('');
                    let line = '';
                    let lineCount = 0;
                    
                    for (let n = 0; n < words.length; n++) {
                      const testLine = line + words[n];
                      const metrics = ctx.measureText(testLine);
                      const testWidth = metrics.width;
                      
                      if (testWidth > maxWidth && n > 0) {
                        ctx.fillText(line, x, y + (lineCount * lineHeight));
                        line = words[n];
                        lineCount++;
                      } else {
                        line = testLine;
                      }
                    }
                    
                    ctx.fillText(line, x, y + (lineCount * lineHeight));
                    return lineCount + 1;
                  }
                  
                  // 绘制描述文本
                  const descriptionLines = drawWrappedText(description, 30, currentY, width - 60, 24);
                  currentY += descriptionLines * 24 + 40;
                  
                  // 如果有图片，绘制图片
                  if (imgElement && imgElement.src && imgElement.complete) {
                    try {
                      // 计算图片尺寸
                      const imgWidth = Math.min(width - 60, 400);
                      const ratio = imgElement.height / imgElement.width;
                      const imgHeight = imgWidth * ratio;
                      
                      // 绘制图片
                      ctx.drawImage(imgElement, (width - imgWidth) / 2, currentY, imgWidth, imgHeight);
                      currentY += imgHeight + 40;
                    } catch (imgError) {
                      console.error('绘制图片失败:', imgError);
                      // 继续执行，即使图片绘制失败
                    }
                  }
                  
                  // 绘制分析标题
                  ctx.fillStyle = '#333';
                  // 根据是否手写设置不同字体
                  if (isHandwritten) {
                    ctx.font = 'bold 18px "LocalHandwritten", cursive';
                  } else {
                    ctx.font = 'bold 18px "NanoQyongDaSong C", Arial';
                  }
                  ctx.fillText('解梦分析:', 30, currentY);
                  currentY += 30;
                  
                  // 绘制分析内容
                  ctx.fillStyle = '#333';
                  
                  // 根据是否手写设置不同字体
                  if (isHandwritten) {
                    ctx.font = '16px "LocalHandwritten", cursive';
                  } else {
                    ctx.font = '16px "NanoQyongDaSong C", Arial';
                  }
                  
                  // 去除Markdown标记后的纯文本，用于绘制
                  let cleanAnalysisText = analysis;
                  try {
                    // 移除重复的"解梦分析："前缀
                    cleanAnalysisText = cleanAnalysisText.replace(/^解梦分析[:：]\s*/i, '');
                    cleanAnalysisText = cleanAnalysisText.replace(/^解梦分析[:：]\s*/i, '');
                    cleanAnalysisText = cleanAnalysisText.replace(/^梦境分析[:：]\s*/i, '');
                    
                    // 简单移除Markdown标记
                    cleanAnalysisText = cleanAnalysisText
                      .replace(/#{1,6}\s+/g, '') // 移除标题标记
                      .replace(/\*\*|\*/g, '')   // 移除粗体和斜体标记
                      .replace(/`/g, '')         // 移除代码标记
                      .replace(/\[|\]/g, '')     // 移除链接标记
                      .trim();
                  } catch (e) {
                    console.error('处理Markdown文本失败:', e);
                  }
                  
                  const analysisLines = drawWrappedText(cleanAnalysisText, 30, currentY, width - 60, 24);
                  currentY += analysisLines * 24 + 40;
                  
                  // 添加水印
                  ctx.textAlign = 'right';
                  // 根据是否手写设置不同字体
                  if (isHandwritten) {
                    ctx.font = 'italic 24px "LocalHandwritten", cursive';
                  } else {
                    ctx.font = 'italic 24px "NanoQyongDaSong C", serif';
                  }
                  ctx.fillStyle = 'rgba(0,0,0,0.1)';
                  ctx.fillText('画梦录', width - 30, height - 30);
                  
                  resolve(canvas);
                } catch (err) {
                  console.error('内置截图工具失败:', err);
                  reject(err);
                }
              });
            };
            
            console.log('已加载内置截图工具');
          }
        } catch (scriptError) {
          console.error('准备截图工具失败:', scriptError);
          throw new Error('准备截图功能失败: ' + scriptError.message);
        }
        
        // 预处理：确保所有图片加载完成
        loadingText.textContent = '正在加载图片资源...';
        const images = filmElement.getElementsByTagName('img');
        const imagePromises = Array.from(images).map(img => {
          if (img.src && !img.complete) {
            img.crossOrigin = 'anonymous';
            return new Promise((resolve) => {
              const originalSrc = img.src;
              img.onload = resolve;
              img.onerror = () => {
                console.warn('图片加载失败:', originalSrc);
                resolve(); // 继续处理，即使图片加载失败
              };
              // 添加时间戳避免缓存
              img.src = originalSrc.split('?')[0] + '?t=' + Date.now();
            });
          }
          return Promise.resolve();
        });
        
        await Promise.all(imagePromises);
        await new Promise(resolve => setTimeout(resolve, 500)); // 额外等待时间确保渲染完成
        
        // 修改样式以确保捕获完整内容
        try {
          filmElement.style.height = 'auto';
          filmElement.style.maxHeight = 'none';
          filmElement.style.overflow = 'visible';
        } catch (styleError) {
          console.error('修改样式失败:', styleError);
          throw new Error('修改样式失败: ' + styleError.message);
        }
        
        // 获取元素完整尺寸
        let filmElementHeight, filmElementWidth;
        try {
          filmElementHeight = filmElement.scrollHeight;
          filmElementWidth = filmElement.scrollWidth;
        } catch (dimensionError) {
          console.error('获取元素尺寸失败:', dimensionError);
          throw new Error('获取元素尺寸失败: ' + dimensionError.message);
        }
        
        loadingText.textContent = '正在生成长图...';
        
        // 使用html2canvas生成长图
        let canvas;
        try {
          canvas = await html2canvas(filmElement, {
            useCORS: true,
            allowTaint: true,
            scale: window.devicePixelRatio || 2,
            logging: true,
            backgroundColor: '#e6f2ff',
            width: filmElementWidth,
            height: filmElementHeight,
            windowWidth: document.documentElement.offsetWidth,
            windowHeight: filmElementHeight,
            scrollX: 0,
            scrollY: 0,
            onclone: (clonedDoc) => {
              const clonedElement = clonedDoc.querySelector('.dream-film');
              if (clonedElement) {
                clonedElement.style.height = 'auto';
                clonedElement.style.maxHeight = 'none';
                clonedElement.style.overflow = 'visible';
              }
            }
          });
        } catch (canvasError) {
          console.error('html2canvas生成失败:', canvasError);
          throw new Error('生成图片失败: ' + canvasError.message);
        }
        
        // 检查并处理超大画布
        if (canvas.height > 16384) {
          console.warn('画布高度超过浏览器限制，正在压缩...');
          
          const maxHeight = 16000;
          const ratio = maxHeight / canvas.height;
          const newWidth = canvas.width * ratio;
          
          const compressedCanvas = document.createElement('canvas');
          compressedCanvas.width = newWidth;
          compressedCanvas.height = maxHeight;
          
          const ctx = compressedCanvas.getContext('2d');
          ctx.drawImage(canvas, 0, 0, newWidth, maxHeight);
          
          canvas = compressedCanvas;
        }
        
        // 恢复原始样式
        filmElement.style.height = originalStyle.height;
        filmElement.style.maxHeight = originalStyle.maxHeight;
        filmElement.style.overflow = originalStyle.overflow;
        
        // 清除加载提示
        if (loadingText.parentNode) {
          loadingText.parentNode.removeChild(loadingText);
        }
        
        try {
          // 直接导出高质量图片并下载，无需预览
          canvas.toBlob(function(blob) {
            if (blob) {
              const url = URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = '梦境记录_' + new Date().toLocaleDateString('zh-CN').replace(/\//g, '-') + '.png';
              
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              
              setTimeout(() => URL.revokeObjectURL(url), 1000);
              
              // 显示成功消息
              const successMsg = document.createElement('div');
              successMsg.style.padding = '10px';
              successMsg.style.margin = '10px 0';
              successMsg.style.backgroundColor = '#d4edda';
              successMsg.style.color = '#155724';
              successMsg.style.borderRadius = '4px';
              successMsg.style.textAlign = 'center';
              successMsg.textContent = '图片已成功保存！请检查下载文件夹。';
              container.appendChild(successMsg);
              
              // 3秒后自动关闭模态框
              setTimeout(function() {
                document.getElementById('exportModal').classList.remove('active');
              }, 3000);
            } else {
              console.error('Blob创建失败: blob为null');
              alert('保存失败: 无法创建图片文件');
            }
          }, 'image/png', 1.0);
        } catch (downloadError) {
          console.error('下载长图失败:', downloadError);
          alert('保存长图失败: ' + (downloadError.message || '未知错误'));
        }
      } catch (error) {
        console.error('长图生成失败:', error);
        
        // 改进错误信息处理
        let errorMessage = '生成长图失败';
        if (error) {
          if (error.message) {
            errorMessage += ': ' + error.message;
          } else if (typeof error === 'object') {
            try {
              errorMessage += ': ' + JSON.stringify(error);
            } catch (e) {
              errorMessage += ': [无法序列化的对象]';
            }
          } else {
            errorMessage += ': ' + String(error);
          }
        } else {
          errorMessage += ': 未知错误(undefined)';
        }
        
        alert(errorMessage + '\n请检查浏览器控制台获取更多信息');
        
        // 恢复原始样式
        try {
          filmElement.style.height = originalStyle.height;
          filmElement.style.maxHeight = originalStyle.maxHeight;
          filmElement.style.overflow = originalStyle.overflow;
        } catch (e) {
          console.error('恢复样式时出错:', e);
        }
        
        // 清除加载提示
        try {
          if (loadingText && loadingText.parentNode) {
            loadingText.parentNode.removeChild(loadingText);
          }
        } catch (e) {
          console.error('移除加载提示时出错:', e);
        }
      }
    });
    
    // 添加关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.className = 'secondary-button';
    closeBtn.innerHTML = '<i class="fas fa-times"></i> 关闭预览';
    closeBtn.style.margin = '10px';
    closeBtn.style.display = 'inline-block';
    closeBtn.onclick = function() {
      document.getElementById('exportModal').classList.remove('active');
    };
    
    // 添加按钮到容器
    buttonContainer.appendChild(exportImageBtn);
    buttonContainer.appendChild(closeBtn);
    container.appendChild(buttonContainer);
  }
  
  // 创建梦境底片DOM
  function createDreamFilmDOM(description, analysis, imageElement, time, location) {
    // 创建底片容器
    const filmContainer = document.createElement('div');
    filmContainer.className = 'dream-film';
    filmContainer.style.backgroundColor = '#e6f2ff';
    filmContainer.style.border = '1px solid #ddd';
    filmContainer.style.borderRadius = '8px';
    filmContainer.style.padding = '30px';
    filmContainer.style.position = 'relative';
    filmContainer.style.maxWidth = '800px';
    filmContainer.style.margin = '0 auto';
    
    // 获取文字风格设置
    const textStyle = document.getElementById('textStyleSelect') ? 
                     document.getElementById('textStyleSelect').value : 'normal';
    
    // 如果选择了手写风格，为底片容器添加手写字体类
    if (textStyle === 'handwritten') {
      filmContainer.classList.add('use-handwritten');
    }
    
    // 添加头部（标题和时间）
    const header = document.createElement('div');
    header.className = 'dream-film-header';
    header.style.textAlign = 'center';
    header.style.marginBottom = '20px';
    
    const title = document.createElement('div');
    title.className = 'dream-film-title';
    // 标题始终使用手写字体
    title.classList.add('handwritten-text');
    title.style.fontSize = '28px';
    title.style.color = '#333';
    title.style.marginBottom = '5px';
    title.textContent = '冲洗梦的胶片';
    header.appendChild(title);
    
    const date = document.createElement('div');
    date.className = 'dream-film-date';
    // 根据文字风格设置日期字体
    if (textStyle === 'handwritten') {
      date.classList.add('handwritten-text');
    }
    date.style.color = '#666';
    date.style.fontSize = '14px';
    const formattedDate = time ? new Date(time).toLocaleString('zh-CN') : new Date().toLocaleString('zh-CN');
    date.textContent = `记录时间: ${formattedDate}`;
    if (location) {
      date.textContent += ` · 地点: ${location}`;
    }
    header.appendChild(date);
    
    filmContainer.appendChild(header);
    
    // 添加内容区域
    const content = document.createElement('div');
    content.className = 'dream-film-content';
    content.style.display = 'flex';
    content.style.flexDirection = 'column';
    content.style.gap = '20px';
    
    // 梦境描述
    if (description) {
      const descriptionDiv = document.createElement('div');
      descriptionDiv.className = 'dream-film-description';
      
      // 根据文字风格设置不同的样式
      if (textStyle === 'handwritten') {
        descriptionDiv.classList.add('handwritten-text');
      }
      
      descriptionDiv.style.fontSize = '18px';
      descriptionDiv.style.color = '#333';
      descriptionDiv.style.backgroundColor = '#fff';
      descriptionDiv.style.padding = '20px';
      descriptionDiv.style.borderRadius = '8px';
      descriptionDiv.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
      descriptionDiv.style.lineHeight = '1.8';
      descriptionDiv.textContent = description;
      content.appendChild(descriptionDiv);
    }
    
    // 梦境图片（如果提供了imageElement）
    if (imageElement && imageElement.src && imageElement.src !== window.location.href) {
      try {
        // 尝试直接使用图片
        const imageContainer = document.createElement('div');
        imageContainer.className = 'dream-film-image-container';
        imageContainer.style.textAlign = 'center';
        
        const image = document.createElement('img');
        image.className = 'dream-film-image';
        image.src = imageElement.src;
        image.alt = '梦境图片';
        image.style.maxWidth = '400px';
        image.style.borderRadius = '8px';
        image.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        imageContainer.appendChild(image);
        
        content.appendChild(imageContainer);
      } catch (e) {
        console.error('添加图片失败:', e);
      }
    }
    
    // 梦境分析
    if (analysis && analysis !== '...') {
      const analysisDiv = document.createElement('div');
      analysisDiv.className = 'dream-film-analysis';
      
      // 根据文字风格设置不同的样式
      if (textStyle === 'handwritten') {
        analysisDiv.classList.add('handwritten-text');
      }
      
      analysisDiv.style.fontSize = '18px';
      analysisDiv.style.color = '#444';
      analysisDiv.style.backgroundColor = '#fff';
      analysisDiv.style.padding = '20px';
      analysisDiv.style.borderRadius = '8px';
      analysisDiv.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
      
      // 添加标题
      const analysisTitle = document.createElement('div');
      analysisTitle.style.fontWeight = 'bold';
      analysisTitle.style.marginBottom = '10px';
      analysisTitle.textContent = '解梦分析:';
      analysisDiv.appendChild(analysisTitle);
      
      // 添加分析内容
      const analysisContent = document.createElement('div');
      
      // 使用marked库渲染Markdown，移除原始标记
      try {
        // 尝试使用marked库渲染
        if (window.marked) {
          // 移除重复的"解梦分析："前缀
          let cleanedAnalysis = analysis;
          cleanedAnalysis = cleanedAnalysis.replace(/^解梦分析[:：]\s*/i, '');
          cleanedAnalysis = cleanedAnalysis.replace(/^解梦分析[:：]\s*/i, '');
          cleanedAnalysis = cleanedAnalysis.replace(/^梦境分析[:：]\s*/i, '');
          
          analysisContent.innerHTML = window.marked.parse(cleanedAnalysis);
        } else {
          // 简单处理，移除基本的Markdown标记和重复的标题
          let formattedText = analysis;
          formattedText = formattedText.replace(/^解梦分析[:：]\s*/i, '');
          formattedText = formattedText.replace(/^解梦分析[:：]\s*/i, '');
          formattedText = formattedText.replace(/^梦境分析[:：]\s*/i, '');
          
          formattedText = formattedText
            .replace(/#{1,6}\s+([^\n]+)/g, '<strong>$1</strong>') // 处理标题
            .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')   // 处理粗体
            .replace(/\*([^*]+)\*/g, '<em>$1</em>')               // 处理斜体
            .replace(/`([^`]+)`/g, '<code>$1</code>')             // 处理代码
            .replace(/\n\n/g, '<br><br>');                       // 处理段落
          
          analysisContent.innerHTML = formattedText;
        }
      } catch (e) {
        // 出错时回退到纯文本显示
        console.error('渲染Markdown失败:', e);
        let cleanedAnalysis = analysis;
        cleanedAnalysis = cleanedAnalysis.replace(/^解梦分析[:：]\s*/i, '');
        cleanedAnalysis = cleanedAnalysis.replace(/^解梦分析[:：]\s*/i, '');
        cleanedAnalysis = cleanedAnalysis.replace(/^梦境分析[:：]\s*/i, '');
        analysisContent.textContent = cleanedAnalysis;
      }
      
      // 根据文字风格设置分析内容的字体
      if (textStyle === 'handwritten') {
        // 使用与父元素相同的手写字体
        analysisContent.classList.add('handwritten-text');
      } else {
        // 非手写风格时使用默认字体
        analysisContent.style.fontFamily = '"NanoQyongDaSong C", sans-serif';
      }
      
      analysisDiv.appendChild(analysisContent);
      
      content.appendChild(analysisDiv);
    }
    
    filmContainer.appendChild(content);
    
    // 添加页脚
    const footer = document.createElement('div');
    footer.className = 'dream-film-footer';
    footer.style.textAlign = 'right';
    footer.style.marginTop = '20px';
    // 根据文字风格设置页脚字体
    if (textStyle === 'handwritten') {
      footer.classList.add('handwritten-text');
    }
    footer.style.fontSize = '14px';
    footer.style.color = '#888';
    footer.textContent = '— 由「画梦录」生成 —';
    filmContainer.appendChild(footer);
    
    // 添加水印
    const watermark = document.createElement('div');
    watermark.className = 'dream-film-watermark';
    watermark.style.position = 'absolute';
    watermark.style.bottom = '20px';
    watermark.style.right = '20px';
    watermark.style.opacity = '0.2';
    
    // 根据文字风格设置水印字体
    if (textStyle === 'handwritten') {
      watermark.classList.add('handwritten-text');
    }
    
    watermark.style.fontSize = '24px';
    watermark.style.color = '#333';
    watermark.style.transform = 'rotate(-15deg)';
    watermark.textContent = '画梦录';
    filmContainer.appendChild(watermark);
    
    return filmContainer;
  }
  
  // 显示手动截图说明
  function showManualCaptureInstructions(container) {
    console.log('开始显示截图说明');
    
    if (!container) {
      console.error('容器对象为空');
      alert('错误：找不到显示区域');
      return;
    }
    
    // 备份原始内容
    const originalContent = container.innerHTML;
    console.log('已备份原始内容');
    
    try {
      // 创建手动截图说明
      const manualDiv = document.createElement('div');
      manualDiv.style.backgroundColor = '#f0f9ff';
      manualDiv.style.color = '#0c5460';
      manualDiv.style.padding = '15px';
      manualDiv.style.borderRadius = '8px';
      manualDiv.style.marginTop = '20px';
      manualDiv.style.textAlign = 'center';
      
      manualDiv.innerHTML = `
        <p style="font-weight: bold; margin-bottom: 10px; font-size: 16px;">请使用系统截图工具保存底片</p>
        <ol style="text-align: left; padding-left: 20px;">
          <li>按下 <strong>Win+Shift+S</strong> 组合键打开截图工具</li>
          <li>选择"矩形截图"模式</li>
          <li>框选整个梦境底片区域</li>
          <li>截图后会自动复制到剪贴板</li>
          <li>打开任意图片编辑器(如画图)，按<strong>Ctrl+V</strong>粘贴</li>
          <li>保存为PNG或JPG格式</li>
        </ol>
      `;
      
      // 创建按钮容器
      const buttonContainer = document.createElement('div');
      buttonContainer.style.textAlign = 'center';
      buttonContainer.style.marginTop = '20px';
      
      // 添加返回按钮
      const backBtn = document.createElement('button');
      backBtn.className = 'secondary-button';
      backBtn.innerHTML = '<i class="fas fa-arrow-left"></i> 返回底片预览';
      backBtn.style.margin = '10px';
      backBtn.style.display = 'inline-block';
      backBtn.onclick = function() {
        // 恢复原始内容
        container.innerHTML = originalContent;
      };
      
      // 添加关闭按钮
      const closeBtn = document.createElement('button');
      closeBtn.className = 'secondary-button';
      closeBtn.innerHTML = '<i class="fas fa-times"></i> 关闭预览';
      closeBtn.style.margin = '10px';
      closeBtn.style.display = 'inline-block';
      closeBtn.onclick = function() {
        document.getElementById('exportModal').classList.remove('active');
      };
      
      // 添加按钮到容器
      buttonContainer.appendChild(backBtn);
      buttonContainer.appendChild(closeBtn);
      
      // 清空并显示新内容
      console.log('准备清空容器并添加新内容');
      container.innerHTML = '';
      container.appendChild(manualDiv);
      container.appendChild(buttonContainer);
      console.log('已显示截图说明');
    } catch (error) {
      console.error('创建截图说明时出错:', error);
      // 恢复原始内容
      container.innerHTML = originalContent;
      alert('无法显示截图说明: ' + error.message);
    }
  }

  // 直接导出底片为图片的函数
  async function exportFilmAsImage(filmElement, container) {
    const loadingText = document.createElement('div');
    loadingText.textContent = '正在生成图片，请稍候...';
    loadingText.style.textAlign = 'center';
    loadingText.style.padding = '10px';
    
    // 先显示底片元素，以确保内容可见，然后再添加加载提示
    container.innerHTML = '';
    container.appendChild(filmElement);
    container.appendChild(loadingText);
    
    // 预加载字体
    const textStyle = document.getElementById('textStyleSelect') ? 
                     document.getElementById('textStyleSelect').value : 'normal';
    
    if (textStyle === 'handwritten') {
      loadingText.textContent = '正在加载字体资源...';
      
      try {
        // 使用FontFace API预加载字体
        const font = new FontFace('LocalHandwritten', 'url("./fonts/NiHeWoDeLangManYuZhou-2.ttf")');
        await font.load();
        document.fonts.add(font);
        console.log('手写字体预加载成功');
      } catch (fontError) {
        console.warn('手写字体预加载失败:', fontError);
        // 继续执行，即使字体加载失败
      }
    }
    
    // 给图片加载一些时间
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 保存原始样式
    const originalStyle = {
      height: filmElement.style.height,
      maxHeight: filmElement.style.maxHeight,
      overflow: filmElement.style.overflow
    };
    
    try {
      // 加载html2canvas库如果需要
      try {
        if (typeof html2canvas === 'undefined') {
          loadingText.textContent = '正在准备图片导出...';
          
          // 内置简化版截图工具
          window.html2canvas = function(element, options) {
            return new Promise((resolve, reject) => {
              try {
                console.log('使用内置截图工具...');
                
                // 获取DOM元素内容
                const title = element.querySelector('.dream-film-title')?.textContent || '冲洗梦的胶片';
                const date = element.querySelector('.dream-film-date')?.textContent || '';
                const description = element.querySelector('.dream-film-description')?.textContent || '';
                const analysis = element.querySelector('.dream-film-analysis')?.textContent || '';
                
                // 获取文字风格设置
                const textStyle = document.getElementById('textStyleSelect') ? 
                                 document.getElementById('textStyleSelect').value : 'normal';
                const isHandwritten = textStyle === 'handwritten';
                
                // 尝试获取图片
                const imgElement = element.querySelector('.dream-film-image');
                
                // 设置画布尺寸
                const width = options.width || element.offsetWidth;
                const height = options.height || element.offsetHeight;
                const scale = options.scale || 2;
                
                // 创建画布
                const canvas = document.createElement('canvas');
                canvas.width = width * scale;
                canvas.height = height * scale;
                
                const ctx = canvas.getContext('2d');
                ctx.scale(scale, scale);
                
                // 绘制背景
                ctx.fillStyle = options.backgroundColor || '#e6f2ff';
                ctx.fillRect(0, 0, width, height);
                
                // 绘制边框
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.strokeRect(10, 10, width - 20, height - 20);
                
                // 绘制标题
                ctx.fillStyle = '#333';
                // 根据是否手写设置不同字体
                if (isHandwritten) {
                  ctx.font = 'bold 28px "LocalHandwritten", cursive';
                } else {
                  ctx.font = 'bold 28px "NanoQyongDaSong C", serif';
                }
                ctx.textAlign = 'center';
                ctx.fillText(title, width/2, 50);
                
                // 绘制日期
                ctx.fillStyle = '#666';
                // 根据是否手写设置不同字体
                if (isHandwritten) {
                  ctx.font = '14px "LocalHandwritten", cursive';
                } else {
                  ctx.font = '14px "NanoQyongDaSong C", Arial';
                }
                ctx.fillText(date, width/2, 80);
                
                // 计算内容区域
                let currentY = 120;
                
                // 绘制描述标题
                ctx.fillStyle = '#333';
                // 根据是否手写设置不同字体
                if (isHandwritten) {
                  ctx.font = 'bold 18px "LocalHandwritten", cursive';
                } else {
                  ctx.font = 'bold 18px "NanoQyongDaSong C", Arial';
                }
                ctx.textAlign = 'left';
                ctx.fillText('梦境描述:', 30, currentY);
                currentY += 30;
                
                // 绘制描述内容
                ctx.fillStyle = '#333';
                // 根据是否手写设置不同字体
                if (isHandwritten) {
                  ctx.font = '16px "LocalHandwritten", cursive';
                } else {
                  ctx.font = '16px "NanoQyongDaSong C", Arial';
                }
                
                // 文本换行函数
                function drawWrappedText(text, x, y, maxWidth, lineHeight) {
                  if (!text) return 0;
                  
                  const words = text.split('');
                  let line = '';
                  let lineCount = 0;
                  
                  for (let n = 0; n < words.length; n++) {
                    const testLine = line + words[n];
                    const metrics = ctx.measureText(testLine);
                    const testWidth = metrics.width;
                    
                    if (testWidth > maxWidth && n > 0) {
                      ctx.fillText(line, x, y + (lineCount * lineHeight));
                      line = words[n];
                      lineCount++;
                    } else {
                      line = testLine;
                    }
                  }
                  
                  ctx.fillText(line, x, y + (lineCount * lineHeight));
                  return lineCount + 1;
                }
                
                // 绘制描述文本
                const descriptionLines = drawWrappedText(description, 30, currentY, width - 60, 24);
                currentY += descriptionLines * 24 + 40;
                
                // 如果有图片，绘制图片
                if (imgElement && imgElement.src && imgElement.complete) {
                  try {
                    // 计算图片尺寸
                    const imgWidth = Math.min(width - 60, 400);
                    const ratio = imgElement.height / imgElement.width;
                    const imgHeight = imgWidth * ratio;
                    
                    // 绘制图片
                    ctx.drawImage(imgElement, (width - imgWidth) / 2, currentY, imgWidth, imgHeight);
                    currentY += imgHeight + 40;
                  } catch (imgError) {
                    console.error('绘制图片失败:', imgError);
                    // 继续执行，即使图片绘制失败
                  }
                } else {
                  console.warn('图片未加载完成或不存在:', imgElement?.src);
                }
                
                // 绘制分析标题
                ctx.fillStyle = '#333';
                // 根据是否手写设置不同字体
                if (isHandwritten) {
                  ctx.font = 'bold 18px "LocalHandwritten", cursive';
                } else {
                  ctx.font = 'bold 18px "NanoQyongDaSong C", Arial';
                }
                ctx.fillText('解梦分析:', 30, currentY);
                currentY += 30;
                
                // 绘制分析内容
                ctx.fillStyle = '#333';
                
                // 根据是否手写设置不同字体
                if (isHandwritten) {
                  ctx.font = '16px "LocalHandwritten", cursive';
                } else {
                  ctx.font = '16px "NanoQyongDaSong C", Arial';
                }
                
                // 去除Markdown标记后的纯文本，用于绘制
                let cleanAnalysisText = analysis;
                try {
                  // 移除重复的"解梦分析："前缀
                  cleanAnalysisText = cleanAnalysisText.replace(/^解梦分析[:：]\s*/i, '');
                  cleanAnalysisText = cleanAnalysisText.replace(/^解梦分析[:：]\s*/i, '');
                  cleanAnalysisText = cleanAnalysisText.replace(/^梦境分析[:：]\s*/i, '');
                  
                  // 简单移除Markdown标记
                  cleanAnalysisText = cleanAnalysisText
                    .replace(/#{1,6}\s+/g, '') // 移除标题标记
                    .replace(/\*\*|\*/g, '')   // 移除粗体和斜体标记
                    .replace(/`/g, '')         // 移除代码标记
                    .replace(/\[|\]/g, '')     // 移除链接标记
                    .trim();
                } catch (e) {
                  console.error('处理Markdown文本失败:', e);
                }
                
                const analysisLines = drawWrappedText(cleanAnalysisText, 30, currentY, width - 60, 24);
                currentY += analysisLines * 24 + 40;
                
                // 添加水印
                ctx.textAlign = 'right';
                // 根据是否手写设置不同字体
                if (isHandwritten) {
                  ctx.font = 'italic 24px "LocalHandwritten", cursive';
                } else {
                  ctx.font = 'italic 24px "NanoQyongDaSong C", serif';
                }
                ctx.fillStyle = 'rgba(0,0,0,0.1)';
                ctx.fillText('画梦录', width - 30, height - 30);
                
                resolve(canvas);
              } catch (err) {
                console.error('内置截图工具失败:', err);
                reject(err);
              }
            });
          };
          
          console.log('已加载内置截图工具');
        }
      } catch (scriptError) {
        console.error('准备截图工具失败:', scriptError);
        throw new Error('准备截图功能失败: ' + scriptError.message);
      }
      
      // 预处理：确保所有图片加载完成
      loadingText.textContent = '正在加载图片资源...';
      const images = filmElement.getElementsByTagName('img');
      console.log('找到图片元素数量:', images.length);
      
      // 确保所有图片加载
      if (images.length > 0) {
        for (let i = 0; i < images.length; i++) {
          const img = images[i];
          console.log(`图片 ${i+1}/${images.length}:`, img.src, '已加载?', img.complete);
          img.crossOrigin = 'anonymous';
        }
      }
      
      const imagePromises = Array.from(images).map((img, idx) => {
        if (img.src && !img.complete) {
          console.log(`等待图片 ${idx+1}/${images.length} 加载:`, img.src);
          img.crossOrigin = 'anonymous';
          return new Promise((resolve) => {
            const originalSrc = img.src;
            img.onload = () => {
              console.log(`图片 ${idx+1} 加载完成:`, originalSrc);
              resolve();
            };
            img.onerror = () => {
              console.warn(`图片 ${idx+1} 加载失败:`, originalSrc);
              resolve(); // 继续处理，即使图片加载失败
            };
            // 添加时间戳避免缓存
            if (originalSrc.indexOf('data:') !== 0) { // 不处理data URL
              img.src = originalSrc.split('?')[0] + '?t=' + Date.now();
            }
          });
        } else {
          console.log(`图片 ${idx+1}/${images.length} 已加载完成:`, img.src);
          return Promise.resolve();
        }
      });
      
      await Promise.all(imagePromises);
      // 额外等待时间确保渲染完成
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      
      // 修改样式以确保捕获完整内容
      try {
        filmElement.style.height = 'auto';
        filmElement.style.maxHeight = 'none';
        filmElement.style.overflow = 'visible';
      } catch (styleError) {
        console.error('修改样式失败:', styleError);
        throw new Error('修改样式失败: ' + styleError.message);
      }
      
      // 获取元素完整尺寸
      let filmElementHeight, filmElementWidth;
      try {
        filmElementHeight = filmElement.scrollHeight;
        filmElementWidth = filmElement.scrollWidth;
      } catch (dimensionError) {
        console.error('获取元素尺寸失败:', dimensionError);
        throw new Error('获取元素尺寸失败: ' + dimensionError.message);
      }
      
      loadingText.textContent = '正在生成图片...';
      
      // 使用html2canvas生成图片
      let canvas;
      try {
        console.log('开始生成Canvas...');
        // 显示底片状态检查
        console.log('底片渲染状态:', {
          尺寸: `${filmElementWidth}x${filmElementHeight}`,
          图片数量: images.length,
          图片已加载: Array.from(images).map(img => img.complete).join(',')
        });
        
        canvas = await html2canvas(filmElement, {
          useCORS: true,
          allowTaint: true,
          scale: window.devicePixelRatio || 2,
          logging: true,
          backgroundColor: '#e6f2ff',
          width: filmElementWidth,
          height: filmElementHeight,
          windowWidth: document.documentElement.offsetWidth,
          windowHeight: filmElementHeight,
          scrollX: 0,
          scrollY: 0,
          onclone: (clonedDoc) => {
            console.log('html2canvas克隆DOM中...');
            const clonedElement = clonedDoc.querySelector('.dream-film');
            if (clonedElement) {
              console.log('找到克隆的底片元素');
              clonedElement.style.height = 'auto';
              clonedElement.style.maxHeight = 'none';
              clonedElement.style.overflow = 'visible';
              
              // 检查克隆的图片
              const clonedImages = clonedElement.getElementsByTagName('img');
              console.log('克隆的图片数量:', clonedImages.length);
              Array.from(clonedImages).forEach((img, i) => {
                console.log(`克隆的图片 ${i+1}:`, img.src, '已加载?', img.complete);
              });
            } else {
              console.warn('未找到克隆的底片元素');
            }
          }
        });
        console.log('Canvas生成完成!', canvas.width, 'x', canvas.height);
      } catch (canvasError) {
        console.error('html2canvas生成失败:', canvasError);
        throw new Error('生成图片失败: ' + canvasError.message);
      }
      
      // 检查并处理超大画布
      if (canvas.height > 16384) {
        console.warn('画布高度超过浏览器限制，正在压缩...');
        
        const maxHeight = 16000;
        const ratio = maxHeight / canvas.height;
        const newWidth = canvas.width * ratio;
        
        const compressedCanvas = document.createElement('canvas');
        compressedCanvas.width = newWidth;
        compressedCanvas.height = maxHeight;
        
        const ctx = compressedCanvas.getContext('2d');
        ctx.drawImage(canvas, 0, 0, newWidth, maxHeight);
        
        canvas = compressedCanvas;
      }
      
      // 恢复原始样式
      filmElement.style.height = originalStyle.height;
      filmElement.style.maxHeight = originalStyle.maxHeight;
      filmElement.style.overflow = originalStyle.overflow;
      
      // 清除加载提示
      if (loadingText.parentNode) {
        loadingText.parentNode.removeChild(loadingText);
      }
      
      try {
        // 直接导出高质量图片并下载，无需预览
        canvas.toBlob(function(blob) {
          if (blob) {
            console.log('Blob创建成功，大小:', blob.size, 'bytes');
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = '梦境记录_' + new Date().toLocaleDateString('zh-CN').replace(/\//g, '-') + '.png';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            setTimeout(() => URL.revokeObjectURL(url), 1000);
            
            // 显示成功消息
            const successMsg = document.createElement('div');
            successMsg.style.padding = '10px';
            successMsg.style.margin = '10px 0';
            successMsg.style.backgroundColor = '#d4edda';
            successMsg.style.color = '#155724';
            successMsg.style.borderRadius = '4px';
            successMsg.style.textAlign = 'center';
            successMsg.textContent = '图片已成功保存！请检查下载文件夹。';
            container.appendChild(successMsg);
            
            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.className = 'secondary-button';
            closeBtn.innerHTML = '<i class="fas fa-times"></i> 关闭';
            closeBtn.style.margin = '10px';
            closeBtn.style.display = 'block';
            closeBtn.style.margin = '10px auto';
            closeBtn.onclick = function() {
              document.getElementById('exportModal').classList.remove('active');
            };
            container.appendChild(closeBtn);
            
            // 3秒后自动关闭模态框
            setTimeout(function() {
              document.getElementById('exportModal').classList.remove('active');
            }, 5000);
          } else {
            console.error('Blob创建失败: blob为null');
            alert('保存失败: 无法创建图片文件');
          }
        }, 'image/png', 1.0);
      } catch (downloadError) {
        console.error('下载图片失败:', downloadError);
        alert('保存图片失败: ' + (downloadError.message || '未知错误'));
      }
    } catch (error) {
      console.error('图片生成失败:', error);
      
      // 改进错误信息处理
      let errorMessage = '生成图片失败';
      if (error) {
        if (error.message) {
          errorMessage += ': ' + error.message;
        } else if (typeof error === 'object') {
          try {
            errorMessage += ': ' + JSON.stringify(error);
          } catch (e) {
            errorMessage += ': [无法序列化的对象]';
          }
        } else {
          errorMessage += ': ' + String(error);
        }
      } else {
        errorMessage += ': 未知错误(undefined)';
      }
      
      alert(errorMessage + '\n请检查浏览器控制台获取更多信息');
      
      // 恢复原始样式
      try {
        filmElement.style.height = originalStyle.height;
        filmElement.style.maxHeight = originalStyle.maxHeight;
        filmElement.style.overflow = originalStyle.overflow;
      } catch (e) {
        console.error('恢复样式时出错:', e);
      }
      
      // 清除加载提示
      try {
        if (loadingText && loadingText.parentNode) {
          loadingText.parentNode.removeChild(loadingText);
        }
      } catch (e) {
        console.error('移除加载提示时出错:', e);
      }
      
      // 添加关闭按钮
      const closeBtn = document.createElement('button');
      closeBtn.className = 'secondary-button';
      closeBtn.innerHTML = '<i class="fas fa-times"></i> 关闭';
      closeBtn.style.margin = '10px auto';
      closeBtn.style.display = 'block';
      closeBtn.onclick = function() {
        document.getElementById('exportModal').classList.remove('active');
      };
      container.appendChild(closeBtn);
    }
  }
}); 