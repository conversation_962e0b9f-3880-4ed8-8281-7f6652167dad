# 🤖 AI便利贴 (BRO 3.0)

<div align="center">

![AI便利贴](https://img.shields.io/badge/AI%E4%BE%BF%E5%88%A9%E8%B4%B4-v3.0-blue?style=for-the-badge)
![Chrome Extension](https://img.shields.io/badge/Chrome-Extension-green?style=for-the-badge&logo=googlechrome)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)

**智能阅读助手 | 多AI模型支持 | 非侵入式设计**

[📥 安装指南](#安装指南) • [🚀 快速开始](#快速开始) • [⚙️ 配置说明](#配置说明) • [🔧 开发指南](#开发指南)

</div>

---

## 📖 项目简介

AI便利贴是一款创新型浏览器扩展，将传统便利贴的实用性与现代AI技术完美结合。它就像贴在您电脑上的智能便利贴，能够帮助您总结网页内容、回答问题，同时不会打断您的阅读节奏，体现"以静制动"的设计理念。

### ✨ 核心特色

- 🎯 **非侵入式设计** - 不改变原网页内容，保持自然阅读体验
- 🔄 **多AI模型支持** - 支持OpenAI、Gemini、SiliconFlow等多种AI服务
- 🏠 **本地模型兼容** - 支持Ollama本地部署，保护隐私
- 📊 **智能统计** - 独特的"信心指数"功能，记录使用习惯
- 🌍 **多语言界面** - 支持中文简体/繁体/英文
- 💾 **自动笔记** - 智能保存对话记录，支持一键导出

## 🚀 核心功能

### 1. 智能内容总结
- 一键总结当前网页内容
- 提取关键信息和要点
- 支持长文档智能分析

### 2. 问答交互
- 针对网页内容提问
- 获取AI助手的详细回答
- 支持连续对话和上下文理解

### 3. 多模型支持
| 平台 | 模型 | 特点 |
|------|------|------|
| **OpenAI** | GPT-4O, GPT-4O-mini, GPT-3.5 | 强大的理解和生成能力 |
| **SiliconFlow** | Qwen3-8B, Qwen2.5-72B, DeepSeek-V3 | 中文优化，性价比高 |
| **Google Gemini** | Gemini 2.5 Flash, Gemini 2.0 Flash | 多模态支持，速度快 |
| **OpenRouter** | 多种模型聚合 | 模型选择丰富 |
| **Ollama** | 本地部署模型 | 完全私有，无需联网 |

### 4. 便捷笔记系统
- 自动保存总结和问答内容
- 按网页标题分类管理
- 支持一键导出为文本文件
- 记录使用的AI模型信息

### 5. 信心指数
- 记录使用频率统计
- 形成正能量反馈循环
- 帮助了解AI辅助习惯

## 📥 安装指南

### 系统要求
- **浏览器**: Chrome 114+ 或其他基于Chromium的浏览器
- **操作系统**: Windows, macOS, Linux
- **网络**: 需要网络连接（使用在线AI模型时）

### 安装步骤

#### 方法一：开发者模式安装（推荐）
1. 下载项目源码到本地
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 扩展安装完成，图标出现在工具栏

#### 方法二：打包安装
1. 在扩展管理页面点击"打包扩展程序"
2. 选择项目文件夹，生成.crx文件
3. 将.crx文件拖拽到扩展管理页面完成安装

## 🚀 快速开始

### 第一次使用

1. **点击扩展图标** - 在浏览器工具栏找到AI便利贴图标（智）
2. **配置API密钥** - 点击左上角齿轮图标⚙️进入设置
3. **选择AI平台** - 从下拉菜单选择您要使用的AI服务
4. **输入API密钥** - 在API Key字段输入您的密钥
5. **保存设置** - 点击"保存设置"按钮

### 基本操作

#### 📄 总结网页内容
1. 打开任意网页
2. 点击AI便利贴图标
3. 点击"I'm OK"按钮
4. 等待AI生成总结

#### ❓ 提问交互
1. 在文本框中输入您的问题
2. 点击"Ask"按钮
3. 获取AI的详细回答

#### 📋 管理笔记
- **复制内容**: 点击"复制"按钮
- **清空当前**: 点击"Clear"按钮
- **导出笔记**: 点击"Export Notes"按钮

## ⚙️ 配置说明

### API密钥获取

#### OpenAI
1. 访问 [OpenAI API](https://platform.openai.com/api-keys)
2. 注册账户并创建API密钥
3. 复制密钥到扩展设置中

#### SiliconFlow
1. 访问 [SiliconFlow](https://siliconflow.cn/)
2. 注册账户获取免费额度
3. 在控制台获取API密钥

#### Google Gemini
1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 创建项目并获取API密钥
3. 配置到扩展中

#### OpenRouter
1. 访问 [OpenRouter](https://openrouter.ai/)
2. 注册并充值账户
3. 获取API密钥

#### Ollama（本地模型）
1. 安装 [Ollama](https://ollama.ai/)
2. 下载模型：`ollama pull llama2`
3. 启动服务：`ollama serve`
4. 扩展会自动连接到本地服务

### 高级设置

#### 语言设置
- **中文简体**: 默认语言
- **中文繁体**: 适合港澳台用户
- **English**: 英文界面

#### 字体大小
- **小**: 适合高分辨率屏幕
- **中**: 默认大小
- **大**: 适合视力不佳用户

#### 状态显示
- **开启**: 显示详细的处理状态
- **关闭**: 简洁模式，隐藏状态信息

## 🔧 开发指南

### 项目结构
```
bro/
├── manifest.json          # 扩展配置文件
├── popup.html            # 主界面HTML
├── popup.js              # 主要逻辑代码
├── background.js         # 后台服务脚本
├── content.js            # 内容脚本（当前为空）
├── apiConfig.js          # API配置管理
└── README.md            # 项目文档
```

### 技术栈
- **前端**: HTML5, CSS3, Vanilla JavaScript
- **扩展API**: Chrome Extension Manifest V3
- **存储**: Chrome Storage API
- **网络**: Fetch API
- **UI框架**: 原生CSS + Google Fonts

### 核心功能实现

#### Markdown渲染
```javascript
function markdownToHtml(text) {
  // 将AI输出的Markdown格式转换为HTML
  return text
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // ... 更多格式处理
}
```

#### API调用封装
```javascript
async function callModelAPI(prompt, isContinuation, isQuestion, questionOrSummary) {
  const apiPlatform = document.getElementById('apiPlatform').value;
  const config = apiConfigs[apiPlatform];
  // 统一的API调用接口
}
```

### 自定义开发

#### 添加新的AI平台
1. 在 `apiConfig.js` 中添加配置
2. 在 `popup.js` 中添加API调用逻辑
3. 更新 `manifest.json` 的权限设置

#### 修改界面样式
- 编辑 `popup.html` 中的CSS样式
- 支持自定义主题色彩
- 响应式设计适配

## 🐛 故障排除

### 常见问题

#### 1. 扩展无法加载
**症状**: 扩展图标不显示或点击无反应
**解决方案**:
- 检查Chrome版本是否为114+
- 确认开发者模式已开启
- 重新加载扩展程序

#### 2. API调用失败
**症状**: 显示"请在设置中输入API Key"
**解决方案**:
- 检查API密钥是否正确
- 确认网络连接正常
- 验证API服务商账户余额

#### 3. 侧边栏无法打开
**症状**: 点击图标后无反应
**解决方案**:
- 更新Chrome到最新版本
- 检查扩展权限设置
- 尝试重启浏览器

#### 4. 本地模型连接失败
**症状**: Ollama模型无法连接
**解决方案**:
- 确认Ollama服务已启动
- 检查端口11434是否被占用
- 验证模型是否已下载

### 调试模式
1. 右键扩展图标 → "检查弹出式窗口"
2. 查看Console面板的错误信息
3. 检查Network面板的API请求

## 🤝 贡献指南

### 如何贡献
1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- 使用2空格缩进
- 函数和变量使用驼峰命名
- 添加必要的注释
- 保持代码简洁易读

### 问题反馈
- 使用GitHub Issues报告bug
- 提供详细的复现步骤
- 包含浏览器版本和错误信息

## 📊 与竞品对比

| 特性 | AI便利贴 | ChatGPT扩展 | Notion AI | 传统笔记工具 |
|------|----------|-------------|-----------|--------------|
| 多模型支持 | ✅ | ❌ | ❌ | ❌ |
| 本地模型选项 | ✅ | ❌ | ❌ | ❌ |
| 内容总结 | ✅ | ✅ | ✅ | ❌ |
| 非侵入式体验 | ✅ | ❌ | ❌ | ✅ |
| 自动笔记保存 | ✅ | ❌ | ✅ | ✅ |
| 启动速度 | 快 | 中 | 慢 | 快 |
| 使用统计反馈 | ✅ | ❌ | ❌ | ❌ |
| 无需账户 | ✅ | ❌ | ❌ | ✅/❌ |
| Markdown渲染 | ✅ | ✅ | ✅ | ❌ |
| 多语言支持 | ✅ | ❌ | ❌ | ✅ |

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢所有AI服务提供商的技术支持
- 感谢开源社区的贡献和反馈
- 特别感谢测试用户的宝贵建议

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/your-username/bro)
- **问题反馈**: [GitHub Issues](https://github.com/your-username/bro/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-username/bro/discussions)

---

<div align="center">

**AI便利贴 —— 您的智能阅读助手，让知识获取事半功倍！** 🚀

[![Star this repo](https://img.shields.io/github/stars/your-username/bro?style=social)](https://github.com/your-username/bro)
[![Fork this repo](https://img.shields.io/github/forks/your-username/bro?style=social)](https://github.com/your-username/bro/fork)

</div>