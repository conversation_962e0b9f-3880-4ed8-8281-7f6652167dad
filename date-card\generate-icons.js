// 生成图标并直接保存到icons目录
const fs = require('fs');
const { createCanvas } = require('canvas');
const path = require('path');

// 创建icons目录（如果不存在）
const iconDir = path.join(__dirname, 'icons');
if (!fs.existsSync(iconDir)) {
  fs.mkdirSync(iconDir, { recursive: true });
  console.log(`创建了图标目录: ${iconDir}`);
}

// 图标尺寸
const iconSizes = [16, 48, 128];

// 为每个尺寸生成图标
iconSizes.forEach(size => {
  // 创建画布
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  // 绘制背景
  drawBackground(ctx, size);
  
  // 绘制日期图标
  drawDateIcon(ctx, size);
  
  // 绘制天气图标
  drawWeatherIcon(ctx, size);
  
  // 保存图标文件
  const iconPath = path.join(iconDir, `icon${size}.png`);
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(iconPath, buffer);
  
  console.log(`已生成图标: ${iconPath}`);
});

// 绘制背景
function drawBackground(ctx, size) {
  // 创建渐变背景
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, '#4a6cf7');  // 蓝色
  gradient.addColorStop(1, '#6a11cb');  // 紫色
  
  // 填充圆形背景
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
  ctx.fill();
}

// 绘制日期图标
function drawDateIcon(ctx, size) {
  // 根据尺寸调整比例
  const scale = size / 128;
  
  // 绘制日期数字
  ctx.fillStyle = '#ffffff';
  ctx.font = `bold ${Math.floor(45 * scale)}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  
  // 获取当前日期
  const day = new Date().getDate();
  ctx.fillText(day, size/2, size/2);
}

// 绘制天气图标
function drawWeatherIcon(ctx, size) {
  // 如果图标太小，就不绘制天气图标
  if (size < 32) {
    return;
  }
  
  const scale = size / 128;
  
  // 绘制一个简单的太阳图标在右上角
  ctx.fillStyle = '#FFD700'; // 金色
  
  // 太阳位置和大小
  const sunRadius = Math.floor(12 * scale);
  const sunX = size - sunRadius - Math.floor(10 * scale);
  const sunY = sunRadius + Math.floor(10 * scale);
  
  // 绘制太阳圆形
  ctx.beginPath();
  ctx.arc(sunX, sunY, sunRadius, 0, Math.PI * 2);
  ctx.fill();
}

console.log('所有图标已生成完成！'); 