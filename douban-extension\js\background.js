// 处理来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'analyzeComments') {
    console.log('收到分析评论请求');
    
    const { itemId, itemType, customPrompt, maxCommentsPages, modelId } = request.data;
    let commentsStats = {
      totalComments: 0,
      pagesRetrieved: 0,
      settingPages: maxCommentsPages
    }; // 用于存储评论统计信息
    
    // 获取评论（无需登录）
    if (itemType === 'movie') {
      getMovieComments(itemId, maxCommentsPages)
        .then(result => {
          // 保存评论统计
          commentsStats.totalComments = result.comments.length;
          commentsStats.pagesRetrieved = result.pagesRetrieved;
          
          // 保存原始评论数据用于导出CSV
          const commentsData = result;
          
          // 分析评论
          return analyzeComments(result.comments, itemType, customPrompt, modelId)
            .then(analysisResult => {
              // 返回分析结果和原始评论数据
              sendResponse({ 
                success: true, 
                result: analysisResult,
                stats: commentsStats,
                commentsData: commentsData // 添加原始评论数据
              });
            });
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
    } else {
      getBookComments(itemId, maxCommentsPages)
        .then(result => {
          // 保存评论统计
          commentsStats.totalComments = result.comments.length;
          commentsStats.pagesRetrieved = result.pagesRetrieved;
          
          // 保存原始评论数据用于导出CSV
          const commentsData = result;
          
          // 分析评论
          return analyzeComments(result.comments, itemType, customPrompt, modelId)
            .then(analysisResult => {
              // 返回分析结果和原始评论数据
              sendResponse({ 
                success: true, 
                result: analysisResult,
                stats: commentsStats,
                commentsData: commentsData // 添加原始评论数据
              });
            });
        })
        .catch(error => {
          sendResponse({ success: false, error: error.message });
        });
    }
    
    return true; // 表示将异步发送响应
  } else if (request.action === 'openPopup') {
    // 处理从content script发来的打开弹窗请求
    console.log("收到打开弹窗请求，请用户手动点击扩展图标");
    return true;
  } else if (request.action === 'exportCommentsCSV') {
    // 处理导出CSV的请求
    console.log('收到导出CSV请求');
    
    const commentsData = request.data.commentsData;
    const csvResult = generateCommentsCSV(commentsData);
    
    sendResponse(csvResult);
    return true; // 异步响应
  }
});

// 使用正则表达式从HTML中提取评论
function extractCommentsFromHtml(html) {
  // 使用简单的正则表达式直接提取评论内容
  const comments = [];
  const regex = /<span\s+class="short">([\s\S]*?)<\/span>/gi;
  let match;
  
  while ((match = regex.exec(html)) !== null) {
    const commentText = match[1].trim();
    if (commentText) {
      // 移除评论中的HTML标签
      const cleanText = commentText.replace(/<[^>]*>/g, '');
      comments.push({
        content: cleanText,
        text: cleanText // 同时保留text字段用于兼容之前的分析函数
      });
    }
  }
  
  return comments;
}

// 获取电影评论（无需登录）
function getMovieComments(movieId, maxPages) {
  return new Promise((resolve, reject) => {
    fetchComments(movieId, 'movie', maxPages)
      .then(result => {
        // 返回评论数组和页数信息
        resolve(result);
      })
      .catch(error => {
        reject(error);
      });
  });
}

// 获取图书评论（无需登录）
function getBookComments(bookId, maxPages) {
  return new Promise((resolve, reject) => {
    fetchComments(bookId, 'book', maxPages)
      .then(result => {
        // 返回评论数组和页数信息
        resolve(result);
      })
      .catch(error => {
        reject(error);
      });
  });
}

// 通用评论获取函数（无需登录）
function fetchComments(itemId, type, maxPages) {
  return new Promise((resolve, reject) => {
    const comments = [];
    let start = 0;
    // 如果提供了maxPages则使用，否则默认为5页
    const pagesToFetch = maxPages || 5; 
    let currentPage = 0;
    let actualPagesRetrieved = 0; // 实际获取的页数
    let itemTitle = ''; // 保存项目标题
    
    console.log(`开始获取${type === 'movie' ? '电影' : '图书'}评论，计划获取${pagesToFetch}页`);
    
    const fetchPage = () => {
      currentPage++;
      const domain = type === 'movie' ? 'movie.douban.com' : 'book.douban.com';
      
      console.log(`正在获取第${currentPage}页评论，当前已有${comments.length}条评论`);
      
      // 构建URL，直接访问公开评论页面
      // 尝试不同的URL格式
      let url;
      if (type === 'movie') {
        url = `https://${domain}/subject/${itemId}/comments?start=${start}&limit=20&sort=new_score&status=P`;
      } else {
        // 图书评论URL需要在comments后添加斜杠
        url = `https://${domain}/subject/${itemId}/comments/?start=${start}&limit=20&sort=new_score&status=P`;
      }
      
      fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
      })
      .then(html => {
        // 如果是第一页，尝试提取标题
        if (currentPage === 1 && !itemTitle) {
          const titleMatch = html.match(/<title>(.*?)(?:\(豆瓣\))?<\/title>/);
          if (titleMatch) {
            itemTitle = titleMatch[1].trim();
          }
        }
        
        // 使用改进的函数提取评论
        const extractedComments = extractCommentsFromHtml(html);
        
        if (extractedComments.length === 0) {
          console.log(`没有更多评论，最终获取了${comments.length}条评论，共${actualPagesRetrieved}页`);
          resolve({
            comments: comments,
            pagesRetrieved: actualPagesRetrieved,
            title: itemTitle,
            itemType: type,
            itemId: itemId
          }); // 没有更多评论，返回结果
          return;
        }
        
        // 添加评论到数组
        comments.push(...extractedComments);
        actualPagesRetrieved++; // 实际获取页数+1
        console.log(`第${currentPage}页获取到${extractedComments.length}条评论，总计${comments.length}条`);
        
        // 如果获取到的评论数量少于20，或者已经达到最大页数，表示已经结束
        if (extractedComments.length < 20 || currentPage >= pagesToFetch) {
          console.log(`评论获取完成，最终获取了${comments.length}条评论，共${actualPagesRetrieved}页`);
          resolve({
            comments: comments,
            pagesRetrieved: actualPagesRetrieved,
            title: itemTitle,
            itemType: type,
            itemId: itemId
          });
        } else {
          start += 20;
          console.log(`准备获取下一页评论，延迟1秒...`);
          setTimeout(fetchPage, 1000); // 延迟1秒请求下一页，避免被豆瓣限制
        }
      })
      .catch(error => {
        // 如果已经获取了一些评论，就返回这些评论
        if (comments.length > 0) {
          console.warn('获取更多评论时出错，但已返回部分评论:', error);
          console.log(`由于错误中断，最终获取了${comments.length}条评论，共${actualPagesRetrieved}页`);
          resolve({
            comments: comments,
            pagesRetrieved: actualPagesRetrieved,
            title: itemTitle,
            itemType: type,
            itemId: itemId
          });
        } else {
          reject(new Error('获取评论失败: ' + error.message));
        }
      });
    };
    
    fetchPage();
  });
}

// 分析评论
function analyzeComments(comments, itemType, customPrompt, modelId) {
  return new Promise((resolve, reject) => {
    if (comments.length === 0) {
      reject(new Error('没有找到评论'));
      return;
    }
    
    console.log(`开始分析 ${comments.length} 条评论`);
    
    // 获取API设置
    chrome.storage.local.get(['apiKey', 'useSiliconflow', 'customModelId'], function(data) {
      if (!data.apiKey) {
        reject(new Error('请先在设置中配置API密钥'));
        return;
      }
      
      const apiKey = data.apiKey;
      // 注意：变量名是useSiliconflow而不是useSiliconFlow
      const useSiliconflowApi = data.useSiliconflow !== undefined ? data.useSiliconflow : true;
      
      // 使用硅基流动API或OpenAI API
      const apiUrl = useSiliconflowApi ? 
        'https://api.siliconflow.cn/v1/chat/completions' : 
        'https://api.openai.com/v1/chat/completions';
      
      // 处理模型名称
      let model;
      if (useSiliconflowApi) {
        if (modelId === 'custom' && data.customModelId) {
          model = data.customModelId;
        } else if (modelId) {
          model = modelId;
        } else {
          model = 'Qwen/Qwen2.5-72B-Instruct'; // 默认模型
        }
      } else {
        model = 'gpt-3.5-turbo'; // OpenAI默认模型
      }
      
      // 从评论对象中提取文本内容用于分析
      const commentsText = comments.map(comment => comment.text || comment.content).join('\n');
      let prompt;
      
      // 基础分析导语（不管是否有自定义提示词，都保留这部分）
      let basePrompt;
      if (itemType === 'movie') {
        basePrompt = `这是一份电影评论的数据，请以Markdown格式总结：
1、用二级标题展示"评分关键词"，然后列出电影观众的打分关键词（集中在具体的人物细节、故事情节或者领域属性等，而不是笼统好或者坏）；
2、用二级标题展示"总体印象"，总结观众对作品的印象轮廓和感觉；
3、用二级标题分别展示"优点"和"缺点"，列出观众提到的主要优缺点，尽量使用要点符号列表；
4、如果有其他值得注意的分析点，可以添加更多的二级标题部分。`;
      } else {
        basePrompt = `这是一份读书评论的数据，请以Markdown格式总结：
1、用二级标题展示"评分关键点"，列出读者对本书的打分关键词领域（集中在图书的各种属性，比如可读性、阅读难度、知识点错误、戏剧性，可以根据图书类型选择相应的归纳要点）；
2、用二级标题展示"总体印象"，总结读者对作品的整体印象和感觉；
3、用二级标题分别展示"优点"和"缺点"，列出读者提到的主要优缺点，尽量使用要点符号列表；
4、如果有其他值得注意的分析点，可以添加更多的二级标题部分。`;
      }
      
      // 如果有自定义提示词，则添加到基础提示中
      if (customPrompt && customPrompt.trim()) {
        prompt = `这是一份${itemType === 'movie' ? '电影' : '图书'}评论的数据，请以Markdown格式分析：\n${basePrompt}\n\n额外的分析要求：${customPrompt.trim()}\n\n请确保使用Markdown格式输出，包括适当的标题、列表和强调，以便于阅读。\n\n评论数据：\n${commentsText}`;
      } else {
        prompt = `${basePrompt}\n\n请确保使用Markdown格式输出，包括适当的标题、列表和强调，以便于阅读。\n\n${commentsText}`;
      }
      
      // 打印日志帮助调试
      console.log('使用API:', useSiliconflowApi ? 'SiliconFlow' : 'OpenAI');
      console.log('API URL:', apiUrl);
      console.log('模型:', model);
      console.log('使用自定义提示词:', customPrompt ? '是' : '否');
      console.log(`发送到API的评论总字数约: ${commentsText.length} 字符`);
      
      fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            { role: 'system', content: '你是一个专业的评论分析助手，擅长总结和分析用户对电影和书籍的评论，并以清晰的Markdown格式输出结构化内容。' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          stream: false
        })
      })
      .then(response => {
        if (!response.ok) {
          return response.json().then(errData => {
            throw new Error(`API响应错误(${response.status}): ${errData.error ? errData.error.message : '未知错误'}`);
          });
        }
        return response.json();
      })
      .then(data => {
        if (data.error) {
          reject(new Error('API错误: ' + data.error.message));
        } else if (data.choices && data.choices[0]) {
          console.log('分析完成，API返回成功');
          resolve(data.choices[0].message.content);
        } else {
          reject(new Error('API返回了无效响应'));
        }
      })
      .catch(error => {
        reject(new Error('分析请求失败: ' + error.message));
      });
    });
  });
}

// 生成CSV格式的评论数据
function generateCommentsCSV(commentsData) {
  const { comments, title, itemType, itemId } = commentsData;
  
  if (!comments || comments.length === 0) {
    return { success: false, error: "没有可导出的评论数据" };
  }
  
  try {
    // 添加UTF-8 BOM标记，使Excel正确识别中文
    let csvContent = '\ufeff';
    
    // CSV头行
    csvContent += "用户ID,星级,评论内容\r\n";
    
    // 添加每条评论数据
    comments.forEach(comment => {
      // 处理CSV中的特殊字符：引号需要替换为双引号，内容需要用引号包裹
      const userId = `"${(comment.userId || '').replace(/"/g, '""')}"`;
      const rating = comment.rating || 0;
      const content = `"${(comment.content || '').replace(/"/g, '""').replace(/\n/g, ' ')}"`;
      
      csvContent += `${userId},${rating},${content}\r\n`;
    });
    
    // 生成文件名
    const fileName = `豆瓣${itemType === 'movie' ? '电影' : '图书'}_${title || itemId}_评论.csv`;
    
    return {
      success: true,
      csvContent: csvContent,
      fileName: fileName
    };
  } catch (error) {
    console.error('生成CSV出错:', error);
    return {
      success: false,
      error: "生成CSV数据时发生错误: " + error.message
    };
  }
}

// 侧边栏功能 - 点击扩展图标时打开侧边栏
chrome.action.onClicked.addListener(async (tab) => {
  console.log('🔧 扩展图标被点击，准备打开侧边栏');

  // 检查Chrome版本和API支持
  const chromeVersion = navigator.userAgent.match(/Chrome\/(\d+)/);
  const version = chromeVersion ? parseInt(chromeVersion[1]) : 0;

  console.log(`🔍 检测到Chrome版本: ${version}`);

  if (version < 114) {
    console.log('❌ Chrome版本过低，不支持侧边栏功能');
    return;
  }

  if (!chrome.sidePanel) {
    console.log('❌ sidePanel API不可用');
    return;
  }

  try {
    console.log('📂 尝试打开侧边栏...');

    // 打开侧边栏
    await chrome.sidePanel.open({
      tabId: tab.id
    });

    console.log('✅ 侧边栏已成功打开');
  } catch (error) {
    console.log('❌ 打开侧边栏失败:', error);

    // 如果侧边栏打开失败，尝试打开独立窗口作为备选方案
    try {
      console.log('🔄 尝试打开独立窗口作为备选方案...');

      await chrome.windows.create({
        url: chrome.runtime.getURL('popup.html'),
        type: 'popup',
        width: 400,
        height: 600,
        focused: true
      });

      console.log('✅ 独立窗口已打开');
    } catch (windowError) {
      console.log('❌ 打开独立窗口也失败:', windowError);
    }
  }
});

// 调试信息
console.log('背景脚本初始化完成 - 侧边栏功能已启用');