<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆瓣评论分析器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fas fa-chart-bar logo-icon"></i>
                <h1>豆瓣评论分析器</h1>
            </div>
        </div>
        
        <div class="input-section">
            <div class="input-group">
                <label for="item-id">ID:</label>
                <input type="text" id="item-id" placeholder="电影/图书ID">
            </div>
            <div class="input-group">
                <label for="item-type">类型:</label>
                <select id="item-type">
                    <option value="movie">电影</option>
                    <option value="book">图书</option>
                </select>
            </div>
            
            <div class="current-page-info">
                <p>当前：<span id="current-title">未检测到豆瓣页面</span></p>
                <button id="auto-fill" class="secondary-button">
                    <i class="fas fa-magic"></i> 自动填充
                </button>
            </div>
            
            <div class="custom-prompt-section">
                <p class="section-title">自定义提示词（可选）:</p>
                <textarea id="custom-prompt" placeholder="在这里输入你想要的分析要点，例如：'请重点关注角色塑造和剧情逻辑'或'请分析本书的写作风格和理论深度'等"></textarea>
                <div class="tooltip">
                    <i class="fas fa-info-circle"></i>
                    <span class="tooltip-text">自定义提示词可以帮助API更准确地分析评论，关注你感兴趣的方面</span>
                </div>
            </div>
            
            <div class="comments-pages-section">
                <p class="section-title">评论页数设置:</p>
                <div class="pages-slider-container">
                    <input type="range" id="comments-pages" min="1" max="20" value="5" class="pages-slider">
                    <span id="pages-display">5页</span>
                    <div class="tooltip">
                        <i class="fas fa-info-circle"></i>
                        <span class="tooltip-text">设置要获取的评论页数，每页约20条评论。无需登录最多可以获取约10页，页数越多分析越全面但速度也越慢。</span>
                    </div>
                </div>
            </div>
            
            <p class="note-text">建议：图书抓取13页以内，电影抓取20页</p>
            
            <button id="analyze-button" class="primary-button">
                <i class="fas fa-chart-pie"></i> 开始分析
            </button>
        </div>
        
        <div class="result-section">
            <h2>分析结果</h2>
            <div id="result-container" class="result-box">
                <button id="copy-result" class="copy-button" title="复制结果">
                    <div class="copy-icon"></div>
                </button>
                <div id="copy-success" class="copy-success">已复制!</div>
                <p class="placeholder">分析结果将在这里显示...</p>
            </div>
            <div class="export-options">
                <button id="export-csv" class="secondary-button" disabled>
                    <i class="fas fa-file-csv"></i> 导出评论CSV
                </button>
                <div id="export-success" class="export-success">已生成CSV文件!</div>
            </div>
        </div>
        
        <div class="settings-section">
            <button id="settings-toggle" class="icon-button">
                <i class="fas fa-cog"></i>
            </button>
            <div id="settings-panel" class="hidden">
                <h3>API设置</h3>
                <div class="input-group">
                    <input type="password" id="openai-api-key" placeholder="输入API密钥">
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="use-siliconflow" checked>
                    <label for="use-siliconflow">使用硅基流动API</label>
                </div>
                <div class="model-selection">
                    <label for="model-select">选择模型:</label>
                    <select id="model-select">
                        <option value="Qwen/Qwen2.5-72B-Instruct">Qwen2.5-72B-Instruct (默认)</option>
                        <option value="Qwen/Qwen2.5-72B-Instruct-128K">Qwen2.5-72B-Instruct-128K</option>
                        <option value="deepseek-ai/DeepSeek-V3">DeepSeek-V3</option>
                        <option value="deepseek-ai/DeepSeek-R1">DeepSeek-R1</option>
                        <option value="custom">自定义...</option>
                    </select>
                </div>
                <div class="custom-model-input hidden">
                    <input type="text" id="custom-model-id" placeholder="输入自定义模型ID">
                </div>
                <button id="save-api-key" class="secondary-button">保存</button>
                <div id="api-status" class="status">未设置API密钥</div>
                <div class="quota-info">
                    <p>备注: API请求会消耗配额，请合理使用</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/popup.js"></script>
</body>
</html> 