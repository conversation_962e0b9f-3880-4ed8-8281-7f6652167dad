<!DOCTYPE html>
<html>
<head>
  <title>画梦录</title>
  <meta charset="UTF-8">
  <link rel="stylesheet" href="popup-clean.css">
  <link rel="stylesheet" href="export-styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script>
    // 初始化 jsPDF
    window.addEventListener('load', function() {
      window.jsPDF = window.jspdf.jsPDF;
      
      // 检测本地字体是否加载
      function checkLocalFontLoading() {
        console.log('检查本地字体加载状态...');
        
        // 创建检测元素
        const fontChecker = document.createElement('div');
        fontChecker.style.position = 'absolute';
        fontChecker.style.visibility = 'hidden';
        fontChecker.style.fontFamily = 'LocalHandwritten';
        fontChecker.textContent = '测试文本';
        document.body.appendChild(fontChecker);
        
        // 检查字体是否加载
        setTimeout(function() {
          const computedStyle = window.getComputedStyle(fontChecker);
          const loadedFontFamily = computedStyle.fontFamily;
          
          console.log('加载的字体:', loadedFontFamily);
          
          if (loadedFontFamily.indexOf('LocalHandwritten') !== -1) {
            console.log('本地手写字体已加载成功');
            document.body.classList.add('local-font-loaded');
          } else {
            console.warn('本地手写字体未能加载，将使用备用字体');
            document.body.classList.add('use-fallback-fonts');
          }
          
          // 检查并应用当前的文字风格设置
          setTimeout(function() {
            const textStyleSelect = document.getElementById('textStyleSelect');
            if (textStyleSelect) {
              const currentStyle = textStyleSelect.value;
              if (currentStyle === 'handwritten') {
                document.body.classList.add('use-handwritten');
              } else {
                document.body.classList.remove('use-handwritten');
              }
              console.log('应用文字风格:', currentStyle);
            }
          }, 100);
          
          // 清理检测元素
          document.body.removeChild(fontChecker);
        }, 500);
      }
      
      // 执行字体加载检测
      checkLocalFontLoading();
    });
  </script>
  <script src="https://unpkg.com/jspdf-autotable@3.5.31/dist/jspdf.plugin.autotable.js"></script>
  <!-- 添加本地手写字体样式 -->
  <style>
    /* 移除覆盖字体定义，使用CSS文件中的定义 */
    .local-font-loaded .handwritten-text {
      font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif !important;
    }
    
    .use-fallback-fonts .handwritten-text {
      font-family: 'FallbackHandwritten', KaiTi, 楷体, STKaiti, 华文楷体, sans-serif !important;
    }
  </style>
</head>
<body>
  <!-- 添加字体预加载 -->
  <div style="position: absolute; visibility: hidden; pointer-events: none; height: 0; width: 0; overflow: hidden;">
    <span style="font-family: 'LocalHandwritten';">字体预加载</span>
    <span style="font-family: 'NanoQyongDaSong C';">字体预加载</span>
    <span style="font-family: 'Ma Shan Zheng', cursive;">字体预加载</span>
    <span style="font-family: 'Zhi Mang Xing', cursive;">字体预加载</span>
    <span style="font-family: 'Liu Jian Mao Cao', cursive;">字体预加载</span>
  </div>
  
  <div class="container">
    <div class="header">
      <div class="title-container">
        <h1>画梦 <span class="moon-icon">🌙</span> 录</h1>
      </div>
      <button id="settingsBtn" class="icon-button" title="设置">
        <i class="fas fa-cog"></i>
      </button>
    </div>
    
    <div id="mainContent">
      <label for="dreamTime">时间:</label>
      <input type="datetime-local" id="dreamTime" name="dreamTime">
      
      <label for="dreamLocation">地点:</label>
      <input type="text" id="dreamLocation" name="dreamLocation" placeholder="例如：卧室、森林、外太空...">
      
      <label for="dreamDescription">梦的具体描述:</label>
      <textarea id="dreamDescription" name="dreamDescription" rows="6" placeholder="详细描述你的梦境..."></textarea>
      
      <label for="dreamExtraInfo">绘画风格:</label>
      <select id="dreamExtraInfo" name="dreamExtraInfo">
        <option value="">默认</option>
        <option value="油画风格">油画</option>
        <option value="水彩风格">水彩</option>
        <option value="素描风格">素描</option>
        <option value="浮世绘风格">浮世绘</option>
      </select>
      
      <div class="model-selection">
        <label for="textModelSelect">文本分析模型:</label>
        <select id="textModelSelect">
          <option value="Qwen/Qwen2.5-72B-Instruct" selected>Qwen2.5-72B (默认)</option>
          <option value="deepseek-ai/DeepSeek-V3">DeepSeek-V3</option>
        </select>
      </div>
      
      <div class="model-selection">
        <label for="modelSelect">绘图模型:</label>
        <select id="modelSelect">
          <option value="black-forest-labs/FLUX.1-schnell" selected>FLUX.1-schnell (快速)</option>
          <option value="black-forest-labs/FLUX.1-dev">FLUX.1-dev (高质量)</option>
          <option value="stabilityai/stable-diffusion-3-5-large">Stable Diffusion 3.5</option>
        </select>
      </div>
      
      <div class="model-selection">
        <label for="textStyleSelect">文字风格:</label>
        <select id="textStyleSelect">
          <option value="normal" selected>普通文字</option>
          <option value="handwritten">手写风格</option>
        </select>
      </div>
      
      <button id="submitDream">记录并生成</button>
      
      <div id="results">
        <h2>解梦分析:</h2>
        <p id="dreamAnalysisText">...</p>
        <h2>梦境绘画:</h2>
        <img id="dreamImage" src="" alt="梦境绘画将显示在这里" style="max-width: 100%; display: none;">
        <p id="imageErrorText" style="display: none; color: red;"></p>
        
        <button id="exportDreamBtn" class="secondary-button" style="display: none;">
          <i class="fas fa-download"></i> 导出梦境底片
        </button>
      </div>
      
      <div id="loading" style="display: none;">
          <p>正在处理中，请稍候...</p>
      </div>
    </div>
    
    <div id="settingsContent" style="display: none;">
      <h2>设置</h2>
      
      <div class="form-group">
        <label for="apiKeyInput">硅基流动 API Key:</label>
        <input type="password" id="apiKeyInput" placeholder="输入您的API Key...">
        <button id="saveApiKey" class="secondary-button">保存</button>
      </div>
      
      <div class="form-group">
        <label for="fontSizeSelect">字体大小:</label>
        <select id="fontSizeSelect">
          <option value="small">小</option>
          <option value="medium" selected>中</option>
          <option value="large">大</option>
        </select>
      </div>
      
      <button id="viewRecordsBtn" class="secondary-button">查看梦境记录</button>
      
      <button id="backToMainBtn" class="secondary-button">返回</button>
    </div>
    
    <div id="recordsContent" style="display: none;">
      <h2>梦境记录</h2>
      <div id="dreamRecordsList"></div>
      <button id="backFromRecordsBtn" class="secondary-button">返回</button>
    </div>
  </div>
  
  <!-- 导出底片预览模态框 -->
  <div class="dream-export-modal" id="exportModal">
    <div class="dream-export-content">
      <button class="dream-export-close" id="closeExportModal">&times;</button>
      <h2>梦境底片预览</h2>
      <div id="dreamFilmContainer"></div>
      <div class="dream-export-actions">
        <button id="downloadDreamFilm" class="secondary-button">
          <i class="fas fa-download"></i> 保存图片
        </button>
        <button id="closeExportModalBtn" class="secondary-button">
          <i class="fas fa-times"></i> 关闭
        </button>
      </div>
    </div>
  </div>
  
  <script src="popup.js"></script>
  
  <!-- 图片导出处理脚本 -->
  <script src="film-export.js"></script>
</body>
</html>