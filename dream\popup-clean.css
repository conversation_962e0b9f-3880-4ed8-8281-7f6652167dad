@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

/* 基础样式 */
body {
    font-family: 'NanoQyongDaSong C', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #e6f2ff;
    color: #333;
}

/* 手写体字体定义 */
@font-face {
    font-family: 'LocalHandwritten';
    src: url('fonts/NiHeWoDeLangManYuZhou-2.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'FallbackHandwritten';
    src: url('fonts/NanoQyongDaSong C.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 头部区域 */
.header {
    position: relative;
    text-align: center;
    padding: 20px 0;
}

.title-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 标题样式 */
.header h1 {
    font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif !important;
    margin: 0;
    font-weight: 700;
    letter-spacing: 2px;
    color: #FFF8DC;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-size: 32px;
    text-align: center;
}

.moon-icon {
    margin: 0 8px;
    font-size: 28px;
}

/* 容器样式 */
.container {
    width: 400px;
    min-height: 600px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
    padding: 20px;
    box-sizing: border-box;
}

/* 主内容区域 */
#mainContent {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
}

/* 输入区域 */
label {
    display: block;
    margin-bottom: 8px;
    margin-top: 15px;
    font-weight: 500;
    color: #555;
}

input[type="text"],
input[type="datetime-local"],
textarea,
select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 14px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-sizing: border-box;
    margin-bottom: 10px;
    min-height: 40px;
    line-height: 1.4;
}

/* 专门为select元素设置更合适的高度 */
select {
    min-height: 44px;
    line-height: 1.4;
    padding: 10px 12px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-weight: normal !important;
}

textarea {
    min-height: 100px;
    resize: vertical;
}

input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 模型选择区域 */
.model-selection {
    margin: 15px 0;
}

/* 按钮样式 */
#submitDream,
#exportDreamBtn,
.secondary-button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 15px 0;
    width: 100%;
}

#submitDream {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

#submitDream:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

#exportDreamBtn,
.secondary-button {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

#exportDreamBtn:hover,
.secondary-button:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

/* 设置按钮 */
#settingsBtn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
}

#settingsBtn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 结果显示区域 */
#results {
    margin-top: 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
}

#results h2 {
    color: #333;
    font-size: 20px;
    margin: 20px 0 10px 0;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
    font-weight: 600;
}

#dreamAnalysisText {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #667eea;
    min-height: 60px;
    line-height: 1.6;
    font-size: 16px;
    color: #333;
}

/* Markdown内容样式优化 */
#dreamAnalysisText p {
    font-size: 16px;
    margin: 8px 0;
    line-height: 1.6;
}

#dreamAnalysisText strong {
    font-weight: 600;
    color: #2c3e50;
}

#dreamAnalysisText h1,
#dreamAnalysisText h2,
#dreamAnalysisText h3 {
    font-size: 17px;
    font-weight: 600;
    margin: 12px 0 6px 0;
    color: #2c3e50;
}

#dreamAnalysisText ul,
#dreamAnalysisText ol {
    margin: 8px 0;
    padding-left: 20px;
}

#dreamAnalysisText li {
    font-size: 16px;
    margin: 4px 0;
    line-height: 1.6;
}

/* 手写字体状态下的特殊样式 */
.use-handwritten #dreamAnalysisText,
.use-handwritten #dreamAnalysisText p,
.use-handwritten #dreamAnalysisText li {
    font-size: 20px !important;
    line-height: 1.8 !important;
}

.use-handwritten #dreamAnalysisText h1,
.use-handwritten #dreamAnalysisText h2,
.use-handwritten #dreamAnalysisText h3 {
    font-size: 22px !important;
    line-height: 1.8 !important;
}

.use-handwritten #dreamAnalysisText strong {
    font-size: 20px !important;
}

/* 手写字体状态下的记录列表样式 */
.use-handwritten .dream-record-item p,
.use-handwritten .dream-record-item div {
    font-size: 18px !important;
    line-height: 1.8 !important;
}

.use-handwritten .dream-record-item h3 {
    font-size: 20px !important;
}

.use-handwritten .dream-record-item strong {
    font-size: 18px !important;
}

/* 手写字体状态下的UI菜单样式 */
.use-handwritten label {
    font-size: 18px !important;
    font-weight: 600 !important;
}

.use-handwritten input[type="text"],
.use-handwritten input[type="datetime-local"],
.use-handwritten textarea,
.use-handwritten select {
    font-size: 16px !important;
    padding: 14px !important;
    min-height: 48px !important;
}

.use-handwritten select option {
    font-size: 16px !important;
    padding: 8px !important;
}

.use-handwritten #submitDream,
.use-handwritten #exportDreamBtn,
.use-handwritten .secondary-button {
    font-size: 18px !important;
    padding: 14px 26px !important;
}

.use-handwritten #results h2 {
    font-size: 24px !important;
}

#dreamImage {
    max-width: 100%;
    border-radius: 10px;
    margin: 15px 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 梦境记录相关样式 */
.dream-record-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #667eea;
}

.dream-record-item h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.dream-record-item p,
.dream-record-item div {
    margin: 8px 0;
    line-height: 1.5;
    font-size: 15px;
}

/* 记录中的markdown样式 */
.dream-record-item h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 10px 0 5px 0;
    color: #2c3e50;
}

.dream-record-item strong {
    font-weight: 600;
    color: #2c3e50;
}

.dream-record-item p {
    margin: 6px 0;
}

.dream-image-container {
    margin: 10px 0;
    text-align: center;
}

.dream-image-container img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.dream-image-container img:hover {
    transform: scale(1.02);
}

.image-loading-error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 8px;
    margin: 5px 0;
    font-size: 12px;
}

#imageErrorText {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 设置面板 */
.settings-panel {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid #e1e5e9;
}

.settings-panel h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 450px) {
    .container {
        width: 100%;
        min-height: 100vh;
        border-radius: 0;
        box-shadow: none;
    }
    
    .content {
        margin: 10px;
        border-radius: 10px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        min-width: auto;
    }
}

/* 记录列表样式 */
#dreamRecordsList {
    max-height: 70vh;
    overflow-y: auto;
    padding: 10px 0;
}

#dreamRecordsList::-webkit-scrollbar {
    width: 6px;
}

#dreamRecordsList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#dreamRecordsList::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#dreamRecordsList::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 侧边栏支持 */
@media (min-width: 451px) {
    .container {
        width: 100%;
        max-width: none;
        box-shadow: none;
        border-radius: 0;
        min-height: 100vh;
    }

    .dream-image-container img {
        max-width: 100%;
        height: auto;
    }
}
