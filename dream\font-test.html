<!DOCTYPE html>
<html>
<head>
  <title>字体测试</title>
  <meta charset="UTF-8">
  <style>
    @font-face {
      font-family: 'TestFont';
      src: url('./fonts/NiHeWoDeLangManYuZhou-2.ttf') format('truetype');
      font-weight: normal;
      font-style: normal;
    }
    
    .test-text {
      font-family: 'TestFont', sans-serif;
      font-size: 24px;
      margin: 20px;
    }
    
    .normal-text {
      font-family: sans-serif;
      font-size: 24px;
      margin: 20px;
    }
  </style>
</head>
<body>
  <h1>字体测试</h1>
  <div class="test-text">这是使用手写字体的文本。</div>
  <div class="normal-text">这是使用默认字体的文本。</div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 检测字体是否加载
      const testElement = document.querySelector('.test-text');
      const computedStyle = window.getComputedStyle(testElement);
      console.log('加载的字体:', computedStyle.fontFamily);
      
      // 创建字体加载状态信息
      const statusDiv = document.createElement('div');
      statusDiv.style.margin = '20px';
      statusDiv.style.padding = '10px';
      statusDiv.style.backgroundColor = '#f0f0f0';
      statusDiv.style.border = '1px solid #ccc';
      
      if (computedStyle.fontFamily.includes('TestFont')) {
        statusDiv.textContent = '✓ 字体加载成功!';
        statusDiv.style.color = 'green';
      } else {
        statusDiv.textContent = '✗ 字体加载失败，使用了备用字体';
        statusDiv.style.color = 'red';
      }
      
      document.body.appendChild(statusDiv);
    });
  </script>
</body>
</html> 