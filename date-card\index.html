<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=500, initial-scale=1.0">
    <title>日期天气卡片</title>
    <!-- 添加Weather Icons库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/weather-icons/2.0.10/css/weather-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- 用于生成图标的隐藏Canvas -->
    <canvas id="iconCanvas" style="display: none;"></canvas>
    
    <div class="container">
        <div class="date-weather-card">
            <div class="card-header">
                <div class="date-section">
                    <div class="month-year" id="monthYear"></div>
                    <div class="day-number" id="dayNumber"></div>
                    <div class="weekday" id="weekdayDisplay"></div>
                </div>
                <div class="weather-section">
                    <div class="weather-icon" id="weather-icon">
                        <i class="wi wi-day-sunny"></i>
                    </div>
                    <div class="weather-info">
                        <div class="temperature" id="temperature">--° / --°</div>
                        <div class="weather-desc" id="weather-description">加载中...</div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="art-display">
                    <div class="art-image">
                        <img id="flowerImage" src="" alt="今日花卉艺术">
                        <div class="art-info">
                            <span id="flowerName">加载中...</span>
                            <span id="artStyle">加载中...</span>
                        </div>
                    </div>
                </div>
                <div class="lunar-date" id="lunarDate"></div>
                <div class="holiday-info" id="holidayInfo" style="display: none;">
                    <i class="fas fa-gift"></i>
                    <span></span>
                </div>
            </div>
            <div class="card-footer">
                <div class="location-selector">
                    <select id="location-select">
                        <option value="beijing">北京</option>
                        <option value="chaoyang">朝阳区</option>
                        <option value="haidian">海淀区</option>
                        <option value="dongcheng">东城区</option>
                        <option value="xicheng">西城区</option>
                        <option value="fengtai">丰台区</option>
                        <option value="shijingshan">石景山区</option>
                        <option value="changping">昌平区</option>
                        <option value="daxing">大兴区</option>
                        <option value="tongzhou">通州区</option>
                        <option value="shunyi">顺义区</option>
                    </select>
                    <button id="refreshButton" class="refresh-button" title="刷新天气数据">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="forecast" id="forecast-container">
                    <!-- 预报将由JS动态生成 -->
                </div>
            </div>
        </div>
        
        <div class="control-panel">
            <button id="prevDate" class="control-btn"><i class="fas fa-chevron-left"></i></button>
            <button id="todayDate" class="control-btn">今天</button>
            <button id="nextDate" class="control-btn"><i class="fas fa-chevron-right"></i></button>
        </div>

        <div class="api-selector">
            <label for="image-source">图片来源:</label>
            <select id="image-source">
                <option value="google-ai">🎨 Google AI生成 (免费推荐)</option>
                <option value="unsplash">🌸 Unsplash真实花卉 (备用)</option>
            </select>
            <span class="api-status" id="api-status">✅ 完全免费</span>
        </div>

        <!-- 添加设置按钮和面板 -->
        <button class="settings-btn" id="settingsBtn">
            <i class="fas fa-cog"></i>
        </button>

        <div class="settings-panel" id="settingsPanel">
            <h3>API设置</h3>
            <div class="input-group">
                <input type="password" id="gemini-api-key" placeholder="输入Gemini API密钥">
            </div>
            <button id="save-gemini-key">保存</button>
            <button id="toggle-ai-generation">切换AI生成</button>
            <div class="status" id="api-status"></div>
            <div class="quota-info">
                <p>当前状态: <span id="quota-status">正常</span></p>
                <p>刷新状态: <span id="cooldown-time">可以刷新</span></p>
                <p>备注: 免费API每分钟请求有限制，超出会自动使用备用图像</p>
                <p>解决方法: 建议间隔10秒以上再刷新</p>
            </div>
        </div>
    </div>
    
    <script src="js/script.js"></script>
</body>
</html> 