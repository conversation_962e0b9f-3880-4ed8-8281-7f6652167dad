# 画梦录 - Dream Log

画梦录是一个浏览器扩展，帮助用户记录、分析和可视化他们的梦境。

## 主要功能

- **梦境记录**：记录梦境的时间、地点和详细描述
- **AI解梦分析**：使用先进的AI模型分析梦境内容
- **梦境可视化**：基于梦境描述生成相应的图像
- **数据保存**：将梦境记录保存在本地
- **梦境导出**：以精美的"底片"形式导出梦境记录和分析结果
- **字体切换**：支持普通文字和手写风格两种显示模式

## 技术栈

- HTML/CSS/JavaScript
- 硅基流动API（用于AI分析和图像生成）
- 自定义字体（手写风格）

## 更新日志

### 2023年12月15日
- 添加手写字体支持
- 优化底片导出功能
- 实现字体风格切换功能

### 2023年12月10日
- 初始版本发布
- 基本的梦境记录和分析功能
- 图片生成功能

## 使用方法

1. 填写梦境的时间、地点和详细描述
2. 可选择添加特定的绘画风格描述
3. 选择文本分析和图像生成模型
4. 选择文字风格（普通/手写）
5. 点击"记录并生成"按钮
6. 查看解梦分析和生成的图像
7. 可选择导出为精美的梦境底片

## 开发者

- 画梦录团队

## 安装说明

1. 下载或克隆此代码库到你的本地计算机
2. 打开浏览器的扩展管理页面：
   - Chrome: 在地址栏输入 `chrome://extensions/`
   - Edge: 在地址栏输入 `edge://extensions/`
   - Firefox: 在地址栏输入 `about:addons`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"（Chrome/Edge）或"临时加载附加组件"（Firefox）
5. 选择包含此插件代码的文件夹

## 使用前的准备

此插件已经集成了硅基流动 API，并使用了一个公共的 API 密钥。如果你想使用自己的 API 密钥：

1. 注册一个硅基流动账户并获取 API 密钥：https://www.siliconflow.cn/
2. 打开 `popup.js` 文件
3. 找到 `const SILICONFLOW_API_KEY = 'sk-uakaufumckwvyevxbqiwvlasfluopchztcambehuahcnqoyr';` 这一行
4. 将其替换为你自己的 API 密钥

## 可用的图像生成模型

插件支持三种不同的图像生成模型，各有特点：

1. **FLUX.1-schnell**（默认）：速度快，适合快速生成图像，对中文提示词支持良好
2. **FLUX.1-dev**：质量更高，生成时间稍长，对中文提示词支持良好
3. **Stable Diffusion 3.5**：高质量图像生成，但对中文支持较弱（插件会自动将中文提示词翻译为英文）

## 技术说明

此插件使用了以下技术和 API：

- **梦境分析**：使用硅基流动的 Qwen2.5-72B-Instruct 大语言模型进行梦境解析
- **图像生成**：使用硅基流动提供的多种图像生成模型
- **自动翻译**：当使用 Stable Diffusion 模型且检测到中文输入时，会自动将提示词翻译为英文以获得更好的图像生成效果

## 注意事项

- API 调用可能会产生费用，具体取决于你使用的 API 服务提供商的定价策略
- 请确保安全存储你的 API 密钥，不要将其分享给他人
- 图像生成可能需要几秒到几十秒不等，取决于选择的模型和 AI 服务的响应速度

## 隐私说明

此插件将你的梦境记录保存在浏览器的本地存储中，不会将数据发送到除了硅基流动 API 之外的任何地方。