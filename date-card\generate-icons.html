<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期天气卡片 - 图标生成器</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            background-color: #f0f5ff;
        }
        
        .container {
            max-width: 600px;
            width: 100%;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #4a6cf7;
            text-align: center;
            margin-bottom: 20px;
        }
        
        p {
            color: #333;
            line-height: 1.6;
        }
        
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }
        
        .icon {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .icon-image {
            margin-bottom: 10px;
            border: 1px solid #eee;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        button:hover {
            background-color: #3a5ad7;
            transform: translateY(-2px);
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }
        
        .instruction {
            background-color: #f5f7fb;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
        }
        
        .step {
            margin-bottom: 10px;
        }
        
        .step-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-color: #4a6cf7;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>日期天气卡片 - 图标生成器</h1>
        <p>此页面将帮助您生成浏览器扩展所需的图标文件。生成的图标将显示当前日期和天气符号。</p>
        
        <div class="icon-preview">
            <div class="icon">
                <canvas id="iconCanvas16" width="16" height="16" class="icon-image"></canvas>
                <span>16×16</span>
            </div>
            <div class="icon">
                <canvas id="iconCanvas48" width="48" height="48" class="icon-image"></canvas>
                <span>48×48</span>
            </div>
            <div class="icon">
                <canvas id="iconCanvas128" width="128" height="128" class="icon-image"></canvas>
                <span>128×128</span>
            </div>
        </div>
        
        <div class="buttons">
            <button id="generateAllBtn">一键生成并下载所有图标</button>
            <button id="generateAndSaveBtn">单独下载图标</button>
        </div>
        
        <div class="instruction">
            <div class="step">
                <span class="step-number">1</span>
                <span>点击上方按钮生成并下载图标文件</span>
            </div>
            <div class="step">
                <span class="step-number">2</span>
                <span>将下载的图标放入您的扩展项目的 icons 文件夹中</span>
            </div>
            <div class="step">
                <span class="step-number">3</span>
                <span>确保图标文件名与 manifest.json 中定义的一致（icon16.png, icon48.png, icon128.png）</span>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iconSizes = [16, 48, 128];
            const canvases = {};
            
            // 获取所有画布
            iconSizes.forEach(size => {
                canvases[size] = document.getElementById(`iconCanvas${size}`);
            });
            
            // 初始化生成所有图标
            generateAllIcons();
            
            // 添加生成按钮事件
            document.getElementById('generateAllBtn').addEventListener('click', function() {
                downloadAllIcons();
            });
            
            document.getElementById('generateAndSaveBtn').addEventListener('click', function() {
                showDownloadOptions();
            });
            
            // 生成所有图标
            function generateAllIcons() {
                iconSizes.forEach(size => {
                    generateIcon(size);
                });
            }
            
            // 生成单个图标
            function generateIcon(size) {
                const canvas = canvases[size];
                const ctx = canvas.getContext('2d');
                
                // 清除画布
                ctx.clearRect(0, 0, size, size);
                
                // 绘制背景
                drawBackground(ctx, size);
                
                // 绘制日期图标
                drawDateIcon(ctx, size);
                
                // 绘制天气图标
                drawWeatherIcon(ctx, size);
            }
            
            // 绘制背景
            function drawBackground(ctx, size) {
                // 创建渐变背景
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#4a6cf7');  // 蓝色
                gradient.addColorStop(1, '#6a11cb');  // 紫色
                
                // 填充圆形背景
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // 绘制日期图标
            function drawDateIcon(ctx, size) {
                // 根据尺寸调整比例
                const scale = size / 128;
                
                // 绘制日期数字
                ctx.fillStyle = '#ffffff';
                ctx.font = `bold ${Math.floor(45 * scale)}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // 获取当前日期
                const day = new Date().getDate();
                ctx.fillText(day, size/2, size/2);
            }
            
            // 绘制天气图标
            function drawWeatherIcon(ctx, size) {
                // 如果图标太小，就不绘制天气图标
                if (size < 32) {
                    return;
                }
                
                const scale = size / 128;
                
                // 绘制一个简单的太阳图标在右上角
                ctx.fillStyle = '#FFD700'; // 金色
                
                // 太阳位置和大小
                const sunRadius = Math.floor(12 * scale);
                const sunX = size - sunRadius - Math.floor(10 * scale);
                const sunY = sunRadius + Math.floor(10 * scale);
                
                // 绘制太阳圆形
                ctx.beginPath();
                ctx.arc(sunX, sunY, sunRadius, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // 下载所有图标
            function downloadAllIcons() {
                iconSizes.forEach(size => {
                    downloadIcon(size);
                });
            }
            
            // 下载单个图标
            function downloadIcon(size) {
                const canvas = canvases[size];
                const dataUrl = canvas.toDataURL('image/png');
                
                const downloadLink = document.createElement('a');
                downloadLink.href = dataUrl;
                downloadLink.download = `icon${size}.png`;
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }
            
            // 显示下载选项
            function showDownloadOptions() {
                const container = document.createElement('div');
                container.style.position = 'fixed';
                container.style.top = '0';
                container.style.left = '0';
                container.style.width = '100%';
                container.style.height = '100%';
                container.style.backgroundColor = 'rgba(0,0,0,0.5)';
                container.style.display = 'flex';
                container.style.justifyContent = 'center';
                container.style.alignItems = 'center';
                container.style.zIndex = '1000';
                
                const dialog = document.createElement('div');
                dialog.style.backgroundColor = 'white';
                dialog.style.padding = '20px';
                dialog.style.borderRadius = '10px';
                dialog.style.maxWidth = '400px';
                dialog.style.width = '100%';
                
                const title = document.createElement('h2');
                title.textContent = '选择要下载的图标尺寸';
                title.style.textAlign = 'center';
                title.style.color = '#4a6cf7';
                dialog.appendChild(title);
                
                iconSizes.forEach(size => {
                    const button = document.createElement('button');
                    button.textContent = `下载 ${size}×${size} 图标`;
                    button.style.width = '100%';
                    button.style.marginBottom = '10px';
                    button.style.padding = '10px';
                    
                    button.addEventListener('click', function() {
                        downloadIcon(size);
                        document.body.removeChild(container);
                    });
                    
                    dialog.appendChild(button);
                });
                
                const cancelButton = document.createElement('button');
                cancelButton.textContent = '取消';
                cancelButton.style.width = '100%';
                cancelButton.style.backgroundColor = '#f2f2f2';
                cancelButton.style.color = '#333';
                
                cancelButton.addEventListener('click', function() {
                    document.body.removeChild(container);
                });
                
                dialog.appendChild(cancelButton);
                container.appendChild(dialog);
                document.body.appendChild(container);
            }
        });
    </script>
</body>
</html> 