* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

html, body {
    width: 500px; /* 固定宽度 */
    min-width: 500px; /* 最小宽度 */
    overflow-x: hidden; /* 防止水平滚动 */
    margin: 0;
    padding: 0;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px; /* 设置最小高度 */
    background-color: #f0f5ff;
    background-image: linear-gradient(135deg, #f0f5ff 0%, #e4f0ff 100%);
    padding: 15px;
}

.container {
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

/* 主卡片样式 */
.date-weather-card {
    width: 100%;
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(82, 106, 208, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.date-weather-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(82, 106, 208, 0.3);
}

/* 卡片头部 - 日期和天气信息 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px;
    background: linear-gradient(to right, #4a6cf7, #6a11cb);
    color: white;
    border-radius: 20px 20px 0 0;
}

.date-section {
    display: flex;
    flex-direction: column;
}

.month-year {
    font-size: 18px;
    opacity: 0.9;
    margin-bottom: 5px;
}

.day-number {
    font-size: 52px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.weekday {
    font-size: 18px;
    opacity: 0.9;
}

.weather-section {
    display: flex;
    align-items: center;
}

.weather-icon {
    font-size: 46px;
    margin-right: 12px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.weather-info {
    display: flex;
    flex-direction: column;
}

.temperature {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 3px;
}

.weather-desc {
    font-size: 16px;
    opacity: 0.9;
}

/* 卡片主体 - 农历和节日信息 */
.card-body {
    padding: 25px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 15px;
    border-bottom: 1px solid #eaedf7;
}

.lunar-date {
    font-size: 18px;
    color: #526ad6;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.lunar-date:before {
    content: '农历';
    font-size: 13px;
    background-color: #e8eeff;
    color: #526ad6;
    padding: 3px 8px;
    border-radius: 4px;
    margin-right: 10px;
}

.holiday-info {
    display: flex;
    align-items: center;
    color: #ff6b6b;
    font-size: 18px;
    font-weight: 500;
}

.holiday-info i {
    margin-right: 10px;
}

.holiday-info:empty {
    display: none;
}

/* 卡片底部 - 天气预报 */
.card-footer {
    padding: 25px;
    background-color: #fafbff;
}

.forecast {
    display: flex;
    justify-content: space-between;
    margin-top: 15px; /* 添加顶部间距 */
    min-height: 100px; /* 确保有最小高度 */
    gap: 10px; /* 添加间距 */
}

.forecast-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    text-align: center;
    padding: 10px;
    background-color: #f0f5ff; /* 添加背景色 */
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    margin: 0 5px;
}

.forecast-day {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
    font-weight: bold;
}

.forecast-icon {
    font-size: 24px;
    color: #526ad6;
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
}

.forecast-temp {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

/* 控制按钮样式 */
.control-panel {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10px;
    margin-bottom: 10px; /* 添加底部间距 */
    align-items: center; /* 确保所有元素垂直居中 */
    flex-wrap: wrap; /* 在小屏幕上自动换行 */
}

.control-btn {
    background: white;
    border: none;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    color: #4a6cf7;
    transition: all 0.2s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.control-btn:hover {
    background: #4a6cf7;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(82, 106, 208, 0.3);
}

#todayDate {
    width: auto;
    padding: 0 25px;
    border-radius: 28px;
    font-weight: 500;
}

/* 天气图标样式 */
.fa-sun {
    color: #ffb347;
}

.fa-cloud-sun, .fa-cloud {
    color: #90b6fb;
}

.fa-cloud-rain, .fa-cloud-showers-heavy {
    color: #7cb0ff;
}

.fa-snowflake {
    color: #c9e3ff;
}

/* 地区选择器样式 */
.location-selector {
    padding: 10px 15px;
    border-radius: 28px;
    border: none;
    background: white;
    color: #4a6cf7;
    font-size: 14px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
    height: 38px;
}

.location-selector:hover {
    box-shadow: 0 6px 15px rgba(82, 106, 208, 0.2);
    transform: translateY(-2px);
}

.location-selector:focus {
    box-shadow: 0 6px 15px rgba(82, 106, 208, 0.3);
}

/* 刷新按钮旋转动画 */
@keyframes rotating {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 添加震动动画 */
@keyframes shake {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.rotating {
    animation: rotating 1.5s linear infinite;
}

/* 适应不同屏幕尺寸 */
@media (max-width: 520px) {
    html, body {
        width: 480px;
        min-width: 480px;
    }
    
    .day-number {
        font-size: 44px;
    }
    
    .weather-icon {
        font-size: 40px;
    }
    
    .temperature {
        font-size: 24px;
    }
}

/* 天气图标样式 - 保持一个定义，删除其他的 */
.wi {
    font-size: 48px;
    line-height: 1;
    color: #ffffff;  /* 更改为白色以适应蓝色背景 */
}

.weather-section .wi {
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.forecast-icon .wi {
    font-size: 24px;
    color: #526ad6;  /* 预报图标为蓝色 */
    margin-bottom: 5px;
}

/* 更新刷新按钮样式 */
.refresh-button {
    background: none;
    border: none;
    color: #ffffff;  /* 更改为白色 */
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
    margin-left: 8px;
    transition: transform 0.2s ease;
}

.refresh-button:hover {
    transform: scale(1.1);
}

.location-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

#location-select {
    padding: 5px 10px;
    border-radius: 15px;
    border: 1px solid #ddd;
    background-color: white;
    color: #333;
    font-size: 14px;
}

/* 预报样式 */
.forecast-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 5px;
    padding: 5px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.1);
}

.forecast-icon .wi {
    font-size: 24px;
}

.forecast-temp {
    font-size: 14px;
    margin-top: 5px;
}

.forecast-desc {
    font-size: 12px;
    color: #ccc;
    margin-top: 2px;
}

.forecast-day {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}

/* 天气图标容器 */
#weather-icon {
    margin-bottom: 10px;
    text-align: center;
}

/* 天气描述文本 */
#weather-description {
    font-size: 16px;
    text-align: center;
    color: #666;
}

/* 确保预报图标正确显示 */
#forecast-container .forecast-icon .wi {
    font-size: 28px;
    color: #526ad6;
    text-shadow: none;
    display: inline-block;
}

.art-display {
    width: 100%;
    margin: 15px 0;
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.art-image {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
    border-radius: 8px;
}

.art-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.art-image:hover img {
    transform: scale(1.05);
}

.art-info {
    display: none; /* 隐藏花卉名称和艺术风格文字 */
}

/* .art-info span 样式已移除，因为 .art-info 已隐藏 */

/* API选择器样式 */
.api-selector {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.api-selector label {
    font-weight: 500;
    color: #526ad6;
}

.api-selector select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
    color: #333;
    font-size: 14px;
}

.api-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    background: #e8f5e8;
    color: #2e7d32;
}

.api-config {
    margin-top: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.api-config input {
    flex: 1;
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.api-config button {
    padding: 5px 10px;
    background: #4a6cf7;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.api-config button:hover {
    background: #3a5ad7;
}

/* 设置按钮 */
.settings-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4a6cf7;
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(82, 106, 208, 0.2);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-btn:hover {
    transform: rotate(30deg);
    background: #3a5ad7;
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    bottom: 70px;
    right: 20px;
    width: 300px;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
}

.settings-panel.active {
    display: block;
}

.settings-panel h3 {
    color: #526ad6;
    margin: 0 0 15px 0;
    font-size: 16px;
}

.input-group {
    margin-bottom: 15px;
}

.input-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.settings-panel button {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: none;
    border-radius: 5px;
    background: #4a6cf7;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
}

.settings-panel button:hover {
    background: #3a5ad7;
}

.status {
    font-size: 14px;
    color: #4CAF50;
    margin-top: 10px;
    text-align: center;
}

.quota-info {
    font-size: 12px;
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    background-color: #f5f5f5;
    color: #666;
}

.quota-info p {
    margin: 5px 0;
    line-height: 1.4;
}

#quota-status {
    font-weight: bold;
    color: #4CAF50;
}

#quota-status.exceeded {
    color: #f44336;
} 