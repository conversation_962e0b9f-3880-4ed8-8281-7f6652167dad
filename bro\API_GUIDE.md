# 🔑 API配置指南

## 概述

AI便利贴支持多种AI服务提供商，每个平台都有不同的特点和定价策略。本指南将帮助您选择合适的AI服务并正确配置API密钥。

## 支持的AI平台

### 1. OpenAI
**特点**: 业界领先的AI模型，理解能力强
**成本**: 按使用量付费，相对较高
**推荐模型**: GPT-4O-mini（性价比高）

#### 获取API密钥
1. 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 注册并验证账户
3. 添加付费方式
4. 创建API密钥

#### 配置示例
```
API Platform: OpenAI
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Base URL: https://api.openai.com/v1
Model: gpt-4o-mini
```

### 2. SiliconFlow
**特点**: 中文优化，免费额度，性价比高
**成本**: 有免费额度，付费价格便宜
**推荐模型**: Qwen2.5-72B-Instruct-128K

#### 获取API密钥
1. 访问 [SiliconFlow](https://siliconflow.cn/)
2. 注册账户（手机号验证）
3. 获得免费额度
4. 在控制台生成API密钥

#### 配置示例
```
API Platform: SiliconFlow
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Base URL: https://api.siliconflow.cn/v1
Model: Qwen/Qwen2.5-72B-Instruct-128K
```

### 3. Google Gemini
**特点**: 多模态支持，速度快，免费额度大
**成本**: 有较大免费额度
**推荐模型**: Gemini 2.5 Flash

#### 获取API密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 使用Google账户登录
3. 创建新项目
4. 生成API密钥

#### 配置示例
```
API Platform: Google Gemini
API Key: AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Base URL: https://generativelanguage.googleapis.com/v1beta/models
Model: gemini-2.5-flash-preview-05-20
```

### 4. OpenRouter
**特点**: 聚合多种模型，选择丰富
**成本**: 按模型不同定价
**推荐模型**: GPT-4o-mini（通过OpenRouter）

#### 获取API密钥
1. 访问 [OpenRouter](https://openrouter.ai/)
2. 注册账户
3. 充值账户余额
4. 获取API密钥

#### 配置示例
```
API Platform: OpenRouter
API Key: sk-or-v1-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Base URL: https://openrouter.ai/api/v1
Model: openai/gpt-4o-mini
```

### 5. Ollama（本地模型）
**特点**: 完全本地运行，隐私保护，无网络费用
**成本**: 免费（需要本地计算资源）
**推荐模型**: llama3.1, qwen2.5

#### 安装配置
1. 下载 [Ollama](https://ollama.ai/)
2. 安装到本地系统
3. 下载模型：
   ```bash
   ollama pull llama3.1
   ollama pull qwen2.5:7b
   ```
4. 启动服务：
   ```bash
   ollama serve
   ```

#### 配置示例
```
API Platform: Ollama
API Key: (无需填写)
Base URL: http://127.0.0.1:11434
Model: 自定义模型名称（如：llama3.1）
```

## 成本对比

| 平台 | 免费额度 | 付费价格 | 特点 |
|------|----------|----------|------|
| OpenAI | $5新用户 | $0.15/1M tokens | 质量最高 |
| SiliconFlow | 较大免费额度 | 很便宜 | 中文优化 |
| Google Gemini | 很大免费额度 | 便宜 | 速度快 |
| OpenRouter | $1新用户 | 按模型定价 | 选择丰富 |
| Ollama | 完全免费 | 无 | 本地运行 |

## 推荐配置策略

### 新手用户
1. **首选**: Google Gemini（免费额度大）
2. **备选**: SiliconFlow（中文友好）

### 高频用户
1. **首选**: SiliconFlow（性价比高）
2. **备选**: Ollama（本地部署）

### 隐私敏感用户
1. **首选**: Ollama（完全本地）
2. **备选**: 自建API服务

### 质量要求高的用户
1. **首选**: OpenAI GPT-4O
2. **备选**: OpenRouter多模型

## 安全建议

### API密钥安全
- 不要在公共场所输入API密钥
- 定期轮换API密钥
- 监控API使用量和费用
- 设置使用限额

### 网络安全
- 使用HTTPS连接
- 避免在不安全网络下使用
- 考虑使用VPN保护隐私

## 故障排除

### 常见错误

#### 401 Unauthorized
**原因**: API密钥无效或过期
**解决**: 检查密钥格式，重新生成

#### 429 Too Many Requests
**原因**: 请求频率过高
**解决**: 降低使用频率，升级账户

#### 403 Forbidden
**原因**: 账户余额不足或权限不够
**解决**: 充值账户，检查权限设置

#### 网络连接错误
**原因**: 网络问题或服务器故障
**解决**: 检查网络连接，尝试其他平台

## 使用技巧

### 节省成本
1. 优先使用有免费额度的平台
2. 选择合适的模型（不一定要最贵的）
3. 避免重复请求相同内容
4. 使用本地模型处理敏感内容

### 提高效果
1. 根据任务选择合适的模型
2. 中文内容优先使用中文优化模型
3. 复杂任务使用更强大的模型
4. 简单任务使用轻量级模型

---

**正确配置API后，您就可以充分体验AI便利贴的强大功能了！** 🚀
