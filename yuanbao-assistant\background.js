/**
 * 元宝助手 - 背景脚本
 * 处理扩展的后台逻辑、状态管理和外部通信
 */

// 状态管理: 记录每个标签页的状态
let tabStates = {};
const DEBUG_MODE = true;

// 调试日志函数
function debug(message, data = null) {
  if (!DEBUG_MODE) return;
  
  const prefix = '元宝助手背景: ';
  if (data) {
    console.log(prefix + message, data);
  } else {
    console.log(prefix + message);
  }
}

// 初始化
debug('背景脚本已加载');

// 监听内容脚本消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  debug('收到消息', request);
  
  // 确保消息来自内容脚本
  if (!sender.tab && request.action !== 'getStatus' && request.action !== 'optimize' && request.action !== 'injectContentScript') {
    debug('消息没有关联的标签页，忽略');
    return false;
  }
  
  const tabId = sender.tab ? sender.tab.id : (request.tabId || null);
  
  switch(request.action) {
    case 'answerDetected':
      // 当内容脚本检测到元宝回答时
      debug('收到回答检测通知，来自标签 ' + tabId);
      handleAnswerDetected(tabId, request.detectedText);
      sendResponse({success: true});
      break;
      
    case 'getStatus':
      // 当弹出窗口请求状态时
      debug('收到状态请求，标签 ' + tabId);
      const status = getTabState(request.tabId || tabId);
      debug('返回状态', status);
      sendResponse(status);
      break;
      
    case 'optimize':
      // 当弹出窗口请求优化回答时
      debug('收到优化请求', request);
      optimizeAnswer(request.tabId, request.answer, request.prompt, request.model, request.apiType)
        .then(result => {
          debug('优化完成，发送结果');
          sendResponse({success: true, result: result});
        })
        .catch(error => {
          debug('优化失败', error);
          sendResponse({success: false, error: error.message});
        });
      return true; // 保持消息通道开放，用于异步响应
      
    case 'injectContentScript':
      // 手动向指定标签注入内容脚本
      debug('请求向标签注入内容脚本: ' + request.tabId);
      injectContentScriptToTab(request.tabId)
        .then(result => {
          sendResponse({success: true, result: result});
        })
        .catch(error => {
          debug('脚本注入失败', error);
          sendResponse({success: false, error: error.message});
        });
      return true; // 保持消息通道开放，用于异步响应
  }
  
  return true;
});

// 当标签页更新时，重置其状态
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  // 仅在页面完全加载时重置状态
  if (changeInfo.status === 'complete') {
    debug('标签页已更新，重置状态: ' + tabId);
    resetTabState(tabId);
    
    // 向内容脚本发送页面已加载的通知
    setTimeout(() => {
      try {
        chrome.tabs.sendMessage(tabId, { action: 'pageLoaded' }, function(response) {
          // 如果发送消息失败，可能内容脚本未加载，尝试注入
          if (chrome.runtime.lastError) {
            debug('向内容脚本发送消息失败，可能需要注入脚本', chrome.runtime.lastError);
            // 仅对http和https页面执行注入 (避免对chrome://页面等尝试注入)
            if (tab.url && (tab.url.startsWith('http://') || tab.url.startsWith('https://'))) {
              injectContentScriptToTab(tabId);
            }
          }
        });
      } catch (error) {
        debug('页面加载通知失败', error);
      }
    }, 1000);
  }
});

// 当标签页关闭时，清理其状态
chrome.tabs.onRemoved.addListener(function(tabId) {
  debug('标签页已关闭，清理状态: ' + tabId);
  delete tabStates[tabId];
});

/**
 * 向指定标签页注入内容脚本
 * @param {number} tabId - 标签页ID
 * @returns {Promise} 注入结果
 */
async function injectContentScriptToTab(tabId) {
  debug('开始向标签页注入内容脚本: ' + tabId);
  
  try {
    // 确认标签页存在且有效
    const tab = await chrome.tabs.get(tabId).catch(() => null);
    if (!tab) {
      throw new Error('标签页不存在或已关闭');
    }
    
    // 确认是网页而不是特殊页面
    if (!tab.url || !(tab.url.startsWith('http://') || tab.url.startsWith('https://'))) {
      throw new Error('标签页不是普通网页，无法注入脚本');
    }
    
    // 先注入CSS
    await chrome.scripting.insertCSS({
      target: { tabId: tabId },
      files: ['content.css']
    });
    debug('CSS注入成功');
    
    // 然后注入JavaScript
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['content.js']
    });
    debug('JavaScript注入成功');
    
    // 确认脚本是否正常初始化
    setTimeout(async () => {
      try {
        // 尝试ping内容脚本
        await chrome.tabs.sendMessage(tabId, { action: 'pingContentScript' });
        debug('内容脚本响应正常');
      } catch (error) {
        debug('内容脚本无响应', error);
        // 这里不需要处理，因为我们只是做检查
      }
    }, 500);
    
    return { success: true, message: '内容脚本注入成功' };
  } catch (error) {
    debug('注入脚本失败', error);
    throw error;
  }
}

/**
 * 设置图标状态 (临时使用徽章代替图标)
 */
function setIconState(tabId, state) {
  let badgeText = '';
  let badgeColor = '';
  
  switch(state) {
    case 'waiting':
      badgeText = '等待';
      badgeColor = '#777777';
      break;
      
    case 'detected':
      badgeText = '检测';
      badgeColor = '#FFC107';
      break;
      
    case 'processing':
      badgeText = '处理';
      badgeColor = '#2196F3';
      break;
      
    case 'completed':
      badgeText = '完成';
      badgeColor = '#4CAF50';
      break;
      
    case 'error':
      badgeText = '错误';
      badgeColor = '#F44336';
      break;
      
    default:
      badgeText = '等待';
      badgeColor = '#777777';
  }
  
  try {
    // 使用徽章颜色和文本代替图标
    chrome.action.setBadgeText({ tabId: tabId, text: badgeText });
    chrome.action.setBadgeBackgroundColor({ tabId: tabId, color: badgeColor });
  } catch (error) {
    debug('设置状态标识时出错', error);
  }
}

/**
 * 处理检测到元宝回答的事件
 */
function handleAnswerDetected(tabId, detectedText = null) {
  debug('处理回答检测事件，标签: ' + tabId);
  
  // 如果标签页已经处于非等待状态，不再改变
  const currentState = getTabState(tabId);
  if (currentState.status && currentState.status !== 'waiting') {
    debug('标签已在非等待状态，保持当前状态: ' + currentState.status);
    
    // 即使状态相同，也更新检测到的文本
    if (detectedText) {
      debug('更新已存在状态的检测文本');
      updateTabState(tabId, currentState.status, { 
        ...currentState,
        detectedText: detectedText 
      });
    }
    return;
  }
  
  // 更新状态，包括检测到的文本
  const updateData = detectedText ? { detectedText: detectedText } : {};
  debug('更新状态为已检测到，包含文本:', updateData);
  updateTabState(tabId, 'detected', updateData);
}

/**
 * 获取标签页状态
 */
function getTabState(tabId) {
  if (!tabId) {
    return { status: 'waiting' };
  }
  
  if (!tabStates[tabId]) {
    tabStates[tabId] = { status: 'waiting' };
  }
  return tabStates[tabId];
}

/**
 * 更新标签页状态
 */
function updateTabState(tabId, status, data = {}) {
  debug('更新标签状态', { tabId, status, data });
  
  if (!tabId) {
    debug('无效的标签ID，无法更新状态');
    return;
  }
  
  if (!tabStates[tabId]) {
    tabStates[tabId] = {};
  }
  
  tabStates[tabId] = {
    ...tabStates[tabId],
    status: status,
    ...data
  };
  
  // 更新图标
  setIconState(tabId, status);
}

/**
 * 重置标签页状态
 */
function resetTabState(tabId) {
  tabStates[tabId] = { status: 'waiting' };
  setIconState(tabId, 'waiting');
}

/**
 * 优化回答
 * 根据选择的API类型，使用OpenRouter或原生API处理回答优化
 */
async function optimizeAnswer(tabId, answer, prompt, model, apiType = 'openrouter') {
  if (!answer) {
    debug('没有找到可优化的回答');
    throw new Error('没有找到可优化的回答');
  }
  
  debug('开始优化回答', { 
    answerLength: answer.length,
    promptLength: prompt.length,
    tabId: tabId,
    apiType: apiType
  });
  
  updateTabState(tabId, 'processing');
  
  try {
    // 获取API设置
    const settings = await chrome.storage.sync.get({
      geminiApiKey: '', // OpenRouter API密钥
      geminiGbApiKey: '', // Gemini GB API密钥
      nativeApiKey: '', // 原生API密钥
      selectedModel: 'gemini',
      debugMode: false,
      saveHistory: true,
      defaultPrompt: '请根据提供内容逻辑框架生成回答，要更具体、更细化、富有条理。请注意，不是单纯修改，可以改变文风文体，补充不足。'
    });
    
    debug('已获取API设置');
    
    let apiKey;
    let apiResponse;
    
    // 选择使用哪个API
    if (apiType === 'native') {
      // 使用原生API
      apiKey = settings.nativeApiKey;
      if (!apiKey) {
        debug('缺少原生API密钥');
        const error = new Error('缺少原生API密钥，请在设置中配置');
        error.code = 'NO_API_KEY';
        throw error;
      }
      
      // 使用原生API调用
      apiResponse = await processWithNativeApi(answer, prompt || settings.defaultPrompt, apiKey, model);
    } else if (apiType === 'gemini-gb') {
      // 使用Gemini GB API
      apiKey = settings.geminiGbApiKey;
      if (!apiKey) {
        debug('缺少Gemini GB API密钥');
        const error = new Error('缺少Gemini 2.5 Pro (GB) API密钥，请在设置中配置');
        error.code = 'NO_API_KEY';
        throw error;
      }

      // 使用Gemini GB API调用
      apiResponse = await processWithGeminiGb(answer, prompt || settings.defaultPrompt, apiKey, model);
    } else {
      // 使用OpenRouter API
      apiKey = settings.geminiApiKey;
      if (!apiKey) {
        debug('缺少OpenRouter API密钥');
        const error = new Error('缺少OpenRouter API密钥，请在设置中配置');
        error.code = 'NO_API_KEY';
        throw error;
      }

      // 使用OpenRouter API调用
      apiResponse = await processWithOpenRouter(answer, prompt || settings.defaultPrompt, apiKey, model);
    }
    
    // 如果设置了保存历史记录，则保存当前结果
    if (settings.saveHistory) {
      await saveToHistory(answer, apiResponse.result, prompt || settings.defaultPrompt, model, apiType);
    }
    
    // 更新状态
    debug('处理完成，更新状态');
    updateTabState(tabId, 'completed', { 
      result: apiResponse.result,
      apiDetails: apiResponse.details
    });
    
    return apiResponse.result;
  } catch (error) {
    debug('优化过程出错', error);
    updateTabState(tabId, 'error', { 
      errorMessage: error.message,
      errorCode: error.code || 'UNKNOWN_ERROR',
      errorDetails: error.details || null
    });
    throw error;
  }
}

/**
 * 保存优化结果到历史记录
 * @param {string} original - 原始文本
 * @param {string} optimized - 优化后的文本
 * @param {string} prompt - 使用的提示词
 * @param {string} model - 使用的模型
 * @param {string} apiType - 使用的API类型
 */
async function saveToHistory(original, optimized, prompt, model, apiType = 'openrouter') {
  try {
    debug('开始保存到历史记录', {
      originalLength: original.length,
      optimizedLength: optimized.length,
      promptLength: prompt.length,
      model: model,
      apiType: apiType
    });
    
    // 获取当前历史记录
    const data = await chrome.storage.sync.get({savedResults: []});
    let savedResults = data.savedResults || [];
    
    debug('当前历史记录数量', savedResults.length);
    
    // 创建新的历史记录项
    const historyItem = {
      timestamp: Date.now(),
      original: original.substring(0, 150) + (original.length > 150 ? '...' : ''), // 保存原文摘要
      optimized: optimized,
      prompt: prompt,
      model: model,
      apiType: apiType // 添加API类型字段
    };
    
    // 计算新记录的大小
    const itemSize = JSON.stringify(historyItem).length;
    debug('新历史记录项大小（字节）', itemSize);
    
    // 如果单条记录过大，截断优化结果以避免存储限制问题
    const MAX_ITEM_SIZE = 8000; // 避免单条记录过大
    if (itemSize > MAX_ITEM_SIZE) {
      debug('历史记录项过大，将截断优化结果', {原始大小: itemSize});
      // 计算需要截断的长度
      const excess = itemSize - MAX_ITEM_SIZE;
      const newLength = optimized.length - excess - 100; // 额外减去100个字符作为缓冲
      historyItem.optimized = optimized.substring(0, newLength) + '... [内容已截断]';
      debug('截断后的大小', JSON.stringify(historyItem).length);
    }
    
    // 将新记录添加到数组开头
    savedResults.unshift(historyItem);
    
    // 限制保存的记录数量为20条
    if (savedResults.length > 20) {
      savedResults = savedResults.slice(0, 20);
    }
    
    // 保存更新后的历史记录
    debug('尝试保存历史记录', {记录数: savedResults.length});
    await chrome.storage.sync.set({savedResults: savedResults});
    
    debug('已成功保存优化结果到历史记录', {count: savedResults.length});
    // 测试读取确认存储成功
    const verification = await chrome.storage.sync.get({savedResults: []});
    debug('验证历史记录数量', verification.savedResults.length);
  } catch (error) {
    debug('保存历史记录失败', error);
    
    // 尝试使用local存储而不是sync存储
    try {
      debug('尝试使用local存储作为备选');
      const data = await chrome.storage.local.get({savedResults: []});
      let savedResults = data.savedResults || [];
      
      // 创建新的历史记录项（简短版本）
      const historyItem = {
        timestamp: Date.now(),
        original: original.substring(0, 150) + (original.length > 150 ? '...' : ''),
        optimized: optimized.substring(0, 2000) + (optimized.length > 2000 ? '... [内容已截断]' : ''),
        prompt: prompt.substring(0, 200) + (prompt.length > 200 ? '...' : ''),
        model: model
      };
      
      // 将新记录添加到数组开头
      savedResults.unshift(historyItem);
      
      // 限制保存的记录数量
      if (savedResults.length > 20) {
        savedResults = savedResults.slice(0, 20);
      }
      
      // 使用local存储保存
      await chrome.storage.local.set({savedResults: savedResults});
      debug('已使用local存储成功保存历史记录');
    } catch (localError) {
      debug('使用local存储也失败了', localError);
    }
  }
}

/**
 * 使用OpenRouter API调用AI模型处理文本
 */
async function processWithOpenRouter(text, prompt, apiKey, model = "google/gemini-2.5-pro-exp-03-25:free") {
  debug('使用OpenRouter API处理文本', { 
    textLength: text.length,
    promptLength: prompt.length,
    model: model
  });
  
  try {
    // 获取当前活动标签页，用于发送消息
    let activeTab = null;
    
    try {
      const tabs = await chrome.tabs.query({active: true, currentWindow: true});
      if (tabs.length > 0) {
        activeTab = tabs[0];
      }
    } catch (e) {
      debug('获取活动标签页失败', e);
    }
    
    // 发送阶段1的状态 - 准备数据
    sendProgressUpdate('preparing', '正在准备数据...');
    
    // 构建完整提示词
    const fullPrompt = `${prompt}\n\n原文内容:\n${text}`;
    
    // OpenRouter API 端点
    const apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
    
    // 准备请求体
    const requestBody = {
      model: model,
      messages: [
        {
          role: "user",
          content: fullPrompt
        }
      ]
    };
    
    // 发送阶段2的状态 - 发送请求给模型
    sendProgressUpdate('sending', '正在将请求发送给AI模型...');
    
    debug('发送OpenRouter API请求', { model: model });
    
    // 记录开始时间
    const startTime = Date.now();
    
    // 发送阶段3的状态 - 模型处理中
    sendProgressUpdate('processing', 'AI模型正在处理内容...');
    
    // 发送请求
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://github.com/yuanbao-assistant',
        'X-Title': 'YuanbaoAssistant'
      },
      body: JSON.stringify(requestBody)
    });
    
    // 发送阶段4的状态 - 接收响应
    sendProgressUpdate('receiving', '正在接收AI响应...');
    
    // 计算请求时间
    const requestTime = Date.now() - startTime;
    
    const responseData = await response.json().catch(err => {
      debug('解析API响应JSON出错', err);
      const error = new Error('API响应格式错误: ' + err.message);
      error.code = 'RESPONSE_PARSE_ERROR';
      throw error;
    });
    
    // 发送阶段5的状态 - 处理响应
    sendProgressUpdate('finalizing', '正在处理AI响应...');
    
    // 记录API调用详情，供调试使用
    const apiCallDetails = {
      requestTime: requestTime,
      statusCode: response.status,
      statusText: response.statusText,
      model: requestBody.model,
      headers: {
        'content-type': response.headers.get('content-type'),
        'x-request-id': response.headers.get('x-request-id')
      }
    };
    
    debug('收到OpenRouter API响应', apiCallDetails);
    
    if (!response.ok) {
      debug('OpenRouter API响应错误', responseData);
      const errorMessage = responseData.error?.message || 
                          responseData.message || 
                          `API请求失败: ${response.status} ${response.statusText}`;
      
      const error = new Error(errorMessage);
      error.code = 'API_ERROR';
      error.details = {
        statusCode: response.status,
        responseBody: responseData,
        requestTime: requestTime
      };
      throw error;
    }
    
    // 提取生成的文本
    if (responseData.choices && 
        responseData.choices[0] && 
        responseData.choices[0].message &&
        responseData.choices[0].message.content) {
      
      // 增加API响应的详细信息
      apiCallDetails.tokenUsage = responseData.usage;
      apiCallDetails.modelUsed = responseData.model;
      apiCallDetails.responseTime = Date.now() - startTime;
      
      // 发送阶段6的状态 - 完成
      sendProgressUpdate('completed', '处理完成!');
      
      return {
        result: responseData.choices[0].message.content,
        details: apiCallDetails
      };
    } else {
      debug('API响应格式异常', responseData);
      const error = new Error('无法从API响应中提取文本');
      error.code = 'INVALID_RESPONSE_FORMAT';
      error.details = responseData;
      throw error;
    }
  } catch (error) {
    debug('OpenRouter API处理失败', error);
    
    // 增强错误信息
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      error.code = 'NETWORK_ERROR';
      error.message = '网络连接错误: 无法连接到OpenRouter API服务器';
    } else if (!error.code) {
      error.code = 'API_PROCESSING_ERROR';
    }
    
    // 发送错误状态
    sendProgressUpdate('error', '处理出错: ' + error.message);
    
    throw error;
  }
  
  // 辅助函数：发送进度更新
  function sendProgressUpdate(status, message) {
    try {
      // 获取模型显示名称
      let modelDisplayName = "AI模型";
      if (model.includes('gemini-2.5-pro')) {
        modelDisplayName = "Gemini 2.5 Pro";
      } else if (model.includes('gemini-2.0-flash-thinking')) {
        modelDisplayName = "Gemini 2.0 Flash Thinking";
      } else if (model.includes('gemini-2.0-flash')) {
        modelDisplayName = "Gemini 2.0 Flash";
      } else if (model.includes('claude-3.7')) {
        modelDisplayName = "Claude 3.7 Sonnet";
      } else if (model.includes('claude-3.5')) {
        modelDisplayName = "Claude 3.5 Sonnet";
      }
      
      // 发送给所有可能打开的popup页面
      chrome.runtime.sendMessage({
        action: 'updateProgressStatus',
        status: status,
        message: message,
        modelName: modelDisplayName
      }).catch(e => {
        // 忽略错误，可能是popup未打开
        debug('发送进度更新消息失败，可能是接收端未准备好', e);
      });
      
      // 添加明确的API状态更新消息，确保popup能够正确显示当前状态
      chrome.runtime.sendMessage({
        action: 'apiStatusUpdate',
        status: status,
        message: message,
        model: modelDisplayName
      }).catch(e => {
        // 忽略错误，可能是popup未打开
        debug('发送API状态更新消息失败，可能是接收端未准备好', e);
      });
    } catch (e) {
      debug('发送进度更新消息异常', e);
    }
  }
}

/**
 * 使用原生Gemini API调用AI模型处理文本
 * @param {string} text - 需要处理的文本
 * @param {string} prompt - 提示词
 * @param {string} apiKey - API密钥
 * @param {string} model - 模型名称，如 "gemini-2.5-pro-exp-03-25"
 */
async function processWithNativeApi(text, prompt, apiKey, model = "gemini-2.5-pro-exp-03-25") {
  debug('使用原生API处理文本', { 
    textLength: text.length,
    promptLength: prompt.length,
    model: model
  });
  
  try {
    // 获取当前活动标签页，用于发送消息
    let activeTab = null;
    
    try {
      const tabs = await chrome.tabs.query({active: true, currentWindow: true});
      if (tabs.length > 0) {
        activeTab = tabs[0];
      }
    } catch (e) {
      debug('获取活动标签页失败', e);
    }
    
    // 发送阶段1的状态 - 准备数据
    sendProgressUpdate('preparing', '正在准备数据...', '原生接口');
    
    // 构建完整提示词
    const fullPrompt = `${prompt}\n\n原文内容:\n${text}`;
    
    // Gemini API 端点（根据具体模型调整）
    const apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/' + model + ':generateContent';
    
    // 准备请求体
    const requestBody = {
      contents: [
        {
          role: "user",
          parts: [
            {
              text: fullPrompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192,
      }
    };
    
    // 发送阶段2的状态 - 发送请求给模型
    sendProgressUpdate('sending', '正在将请求发送给Gemini...', '原生接口');
    
    debug('发送原生Gemini API请求', { model: model });
    
    // 记录开始时间
    const startTime = Date.now();
    
    // 发送阶段3的状态 - 模型处理中
    sendProgressUpdate('processing', 'Gemini正在处理内容...', '原生接口');
    
    // 发送请求
    const response = await fetch(`${apiUrl}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    // 发送阶段4的状态 - 接收响应
    sendProgressUpdate('receiving', '正在接收Gemini响应...', '原生接口');
    
    // 计算请求时间
    const requestTime = Date.now() - startTime;
    
    const responseData = await response.json().catch(err => {
      debug('解析API响应JSON出错', err);
      const error = new Error('API响应格式错误: ' + err.message);
      error.code = 'RESPONSE_PARSE_ERROR';
      throw error;
    });
    
    // 发送阶段5的状态 - 处理响应
    sendProgressUpdate('finalizing', '正在处理Gemini响应...', '原生接口');
    
    // 记录API调用详情，供调试使用
    const apiCallDetails = {
      requestTime: requestTime,
      statusCode: response.status,
      statusText: response.statusText,
      model: model,
      headers: {
        'content-type': response.headers.get('content-type')
      },
      apiType: 'native'
    };
    
    debug('收到原生Gemini API响应', apiCallDetails);
    
    if (!response.ok) {
      debug('原生Gemini API响应错误', responseData);
      const errorMessage = responseData.error?.message || 
                          responseData.message || 
                          `API请求失败: ${response.status} ${response.statusText}`;
      
      const error = new Error(errorMessage);
      error.code = 'API_ERROR';
      error.details = {
        statusCode: response.status,
        responseBody: responseData,
        requestTime: requestTime
      };
      throw error;
    }
    
    // 提取生成的文本
    let generatedText = "";
    
    if (responseData.candidates && 
        responseData.candidates[0] && 
        responseData.candidates[0].content &&
        responseData.candidates[0].content.parts) {
      // 从parts数组中提取文本
      for (const part of responseData.candidates[0].content.parts) {
        if (part.text) {
          generatedText += part.text;
        }
      }
      
      // 增加API响应的详细信息
      apiCallDetails.tokenUsage = responseData.usageMetadata;
      apiCallDetails.modelUsed = model;
      apiCallDetails.responseTime = Date.now() - startTime;
      
      // 发送阶段6的状态 - 完成
      sendProgressUpdate('completed', '处理完成!', '原生接口');
      
      return {
        result: generatedText,
        details: apiCallDetails
      };
    } else {
      debug('API响应格式异常', responseData);
      const error = new Error('无法从API响应中提取文本');
      error.code = 'INVALID_RESPONSE_FORMAT';
      error.details = responseData;
      throw error;
    }
  } catch (error) {
    debug('原生Gemini API处理失败', error);
    
    // 增强错误信息
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      error.code = 'NETWORK_ERROR';
      error.message = '网络连接错误: 无法连接到Gemini API服务器';
    } else if (!error.code) {
      error.code = 'API_PROCESSING_ERROR';
    }
    
    // 发送错误状态
    sendProgressUpdate('error', '处理出错: ' + error.message, '原生接口');
    
    throw error;
  }
  
  // 辅助函数：发送进度更新
  function sendProgressUpdate(status, message, provider = 'Gemini原生') {
    try {
      // 获取模型显示名称
      let modelDisplayName = "Gemini模型";
      if (model.includes('gemini-2.5-pro')) {
        modelDisplayName = "Gemini 2.5 Pro";
      } else if (model.includes('gemini-2.0-flash-thinking')) {
        modelDisplayName = "Gemini 2.0 Flash Thinking";
      } else if (model.includes('gemini-2.0-flash')) {
        modelDisplayName = "Gemini 2.0 Flash";
      }
      
      // 添加原生API标识
      modelDisplayName += " (原生)";
      
      // 发送给所有可能打开的popup页面
      chrome.runtime.sendMessage({
        action: 'updateProgressStatus',
        status: status,
        message: message,
        modelName: modelDisplayName
      }).catch(e => {
        // 忽略错误，可能是popup未打开
        debug('发送进度更新消息失败，可能是接收端未准备好', e);
      });
      
      // 添加明确的API状态更新消息，确保popup能够正确显示当前状态
      chrome.runtime.sendMessage({
        action: 'apiStatusUpdate',
        status: status,
        message: message,
        model: modelDisplayName
      }).catch(e => {
        // 忽略错误，可能是popup未打开
        debug('发送API状态更新消息失败，可能是接收端未准备好', e);
      });
    } catch (e) {
      debug('发送进度更新消息异常', e);
    }
  }
}

/**
 * 使用Gemini 2.5 Pro (GB) API调用AI模型处理文本
 */
async function processWithGeminiGb(text, prompt, apiKey, model = "gemini-2.5-pro") {
  debug('使用Gemini GB API处理文本', {
    textLength: text.length,
    promptLength: prompt.length,
    model: model
  });

  try {
    // 发送阶段1的状态 - 准备数据
    sendProgressUpdate('preparing', '正在准备数据...');

    // 构建完整提示词
    const fullPrompt = `${prompt}\n\n原文内容:\n${text}`;

    // Gemini GB API 端点
    const apiUrl = 'https://iebemjnrzxnv.ap-southeast-1.clawcloudrun.com/v1/chat/completions';

    // 准备请求体
    const requestBody = {
      model: model,
      messages: [
        {
          role: "user",
          content: fullPrompt
        }
      ],
      max_tokens: 4000,
      temperature: 0.7
    };

    // 发送阶段2的状态 - 发送请求给模型
    sendProgressUpdate('sending', '正在将请求发送给Gemini GB模型...');

    debug('发送Gemini GB API请求', { model: model });

    // 记录开始时间
    const startTime = Date.now();

    // 发送阶段3的状态 - 模型处理中
    sendProgressUpdate('processing', 'Gemini GB模型正在处理内容...');

    // 发送请求
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    // 发送阶段4的状态 - 接收响应
    sendProgressUpdate('receiving', '正在接收Gemini GB响应...');

    // 计算请求时间
    const requestTime = Date.now() - startTime;

    const responseData = await response.json().catch(err => {
      debug('解析Gemini GB API响应JSON出错', err);
      const error = new Error('API响应格式错误: ' + err.message);
      error.code = 'RESPONSE_PARSE_ERROR';
      throw error;
    });

    // 发送阶段5的状态 - 处理响应
    sendProgressUpdate('finalizing', '正在处理Gemini GB响应...');

    // 记录API调用详情，供调试使用
    const apiCallDetails = {
      requestTime: requestTime,
      statusCode: response.status,
      statusText: response.statusText,
      model: requestBody.model,
      headers: {
        'content-type': response.headers.get('content-type'),
        'x-request-id': response.headers.get('x-request-id')
      }
    };

    debug('收到Gemini GB API响应', apiCallDetails);

    if (!response.ok) {
      debug('Gemini GB API响应错误', responseData);
      const errorMessage = responseData.error?.message ||
                          responseData.message ||
                          `API请求失败: ${response.status} ${response.statusText}`;

      const error = new Error(errorMessage);
      error.code = 'API_ERROR';
      error.details = {
        statusCode: response.status,
        responseBody: responseData,
        requestTime: requestTime
      };
      throw error;
    }

    // 提取生成的文本
    if (responseData.choices &&
        responseData.choices[0] &&
        responseData.choices[0].message &&
        responseData.choices[0].message.content) {

      // 增加API响应的详细信息
      apiCallDetails.tokenUsage = responseData.usage;
      apiCallDetails.modelUsed = responseData.model;
      apiCallDetails.responseTime = Date.now() - startTime;

      // 发送阶段6的状态 - 完成
      sendProgressUpdate('completed', '处理完成!');

      return {
        result: responseData.choices[0].message.content,
        details: apiCallDetails
      };
    } else {
      debug('Gemini GB API响应格式异常', responseData);
      const error = new Error('无法从API响应中提取文本');
      error.code = 'INVALID_RESPONSE_FORMAT';
      error.details = responseData;
      throw error;
    }
  } catch (error) {
    debug('Gemini GB API处理失败', error);

    // 增强错误信息
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      error.code = 'NETWORK_ERROR';
      error.message = '网络连接错误: 无法连接到Gemini GB API服务器';
    } else if (!error.code) {
      error.code = 'API_PROCESSING_ERROR';
    }

    // 发送错误状态
    sendProgressUpdate('error', '处理出错: ' + error.message);

    throw error;
  }

  // 辅助函数：发送进度更新
  function sendProgressUpdate(status, message) {
    try {
      // 获取模型显示名称
      let modelDisplayName = "Gemini 2.5 Pro (GB)";

      // 发送消息到弹出窗口
      chrome.runtime.sendMessage({
        action: 'progressUpdate',
        status: status,
        message: message,
        model: modelDisplayName
      });
    } catch (e) {
      debug('发送进度更新消息异常', e);
    }
  }
}

// 扩展安装或更新时的处理
chrome.runtime.onInstalled.addListener(function(details) {
  if (details.reason === 'install') {
    debug('扩展首次安装');
    // 可以打开选项页或欢迎页
  } else if (details.reason === 'update') {
    debug('扩展已更新', { oldVersion: details.previousVersion });
  }
});

// 处理扩展图标点击 - 打开侧边栏
chrome.action.onClicked.addListener(async (tab) => {
  debug('扩展图标被点击，当前标签页:', tab.id);

  // 检查Chrome版本和API支持
  if (!chrome.sidePanel) {
    debug('❌ Chrome版本太低，不支持侧边栏API (需要Chrome 114+)');
    console.error('您的Chrome版本不支持侧边栏功能，需要Chrome 114+版本');
    return;
  }

  if (!chrome.sidePanel.open) {
    debug('❌ sidePanel.open API不可用');
    console.error('侧边栏API不可用，请检查Chrome版本');
    return;
  }

  try {
    debug('🔄 尝试打开侧边栏...');
    await chrome.sidePanel.open({ tabId: tab.id });
    debug('✅ 侧边栏已成功打开');
  } catch (error) {
    debug('❌ 打开侧边栏失败:', error);
    console.error('打开侧边栏失败:', error.message);

    // 备用方案：打开独立窗口
    debug('🔄 使用备用方案：打开独立窗口');
    try {
      await chrome.windows.create({
        url: 'popup.html',
        type: 'popup',
        width: 400,
        height: 700,
        left: screen.availWidth - 420,
        top: 100
      });
      debug('✅ 独立窗口已打开');
    } catch (windowError) {
      debug('❌ 打开独立窗口也失败:', windowError);
    }
  }
});

// 调试信息
debug('背景脚本初始化完成 - 侧边栏功能已启用');