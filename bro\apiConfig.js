const apiConfigs = {
  openai: {
    name: "OpenAI",
    baseUrl: "https://api.openai.com/v1",
    models: [
      { id: "gpt-4o", name: "GPT-4O" },
      { id: "gpt-4o-mini", name: "GPT-4O-mini" },
      { id: "gpt-3.5-turbo", name: "GPT-3.5 Turbo" },
      { id: "custom", name: "自定义模型" }
    ]
  },
  gemini: {
    name: "Google Gemini",
    baseUrl: "https://generativelanguage.googleapis.com/v1beta/models",
    models: [
      { id: "gemini-2.5-flash-preview-05-20", name: "Gemini 2.5 Flash (05-20)" },
      { id: "gemini-2.5-flash-preview-04-17", name: "Gemini 2.5 Flash (04-17)" },
      { id: "gemini-2.0-flash-001", name: "Gemini 2.0 Flash" },
      { id: "custom", name: "自定义模型" }
    ]
  },
  siliconflow: {
    name: "<PERSON><PERSON><PERSON>",
    baseUrl: "https://api.siliconflow.cn/v1",
    models: [
      { id: "Qwen/Qwen3-8B", name: "Qwen3-8B" },
      { id: "Qwen/Qwen2.5-72B-Instruct-128K", name: "Qwen2.5-72B" },
      { id: "deepseek-ai/DeepSeek-V3", name: "DeepSeek-V3" },
      { id: "deepseek-ai/DeepSeek-R1", name: "DeepSeek-R1" },
      { id: "custom", name: "自定义模型" }
    ]
  },
  openrouter: {
    name: "OpenRouter",
    baseUrl: "https://openrouter.ai/api/v1",
    models: [
      { id: "openai/gpt-4.1-nano", name: "GPT-4.1-nano" },
      { id: "openai/gpt-4o-mini", name: "GPT-4o-mini" },
      { id: "x-ai/grok-3-mini-beta", name: "Grok-3-mini" },
      { id: "custom", name: "自定义模型" }
    ]
  },
  ollama: {
    name: "Ollama",
    baseUrl: "http://127.0.0.1:11434",
    models: [
      { id: "custom", name: "自定义模型" }
    ]
  }
};

export default apiConfigs;