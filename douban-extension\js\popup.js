document.addEventListener('DOMContentLoaded', function() {
  // DOM 元素
  const itemIdInput = document.getElementById('item-id');
  const itemTypeSelect = document.getElementById('item-type');
  const currentTitleSpan = document.getElementById('current-title');
  const autoFillButton = document.getElementById('auto-fill');
  const analyzeButton = document.getElementById('analyze-button');
  const resultContainer = document.getElementById('result-container');
  const settingsToggle = document.getElementById('settings-toggle');
  const settingsPanel = document.getElementById('settings-panel');
  const apiKeyInput = document.getElementById('openai-api-key');
  const saveApiKeyButton = document.getElementById('save-api-key');
  const apiStatus = document.getElementById('api-status');
  const useSiliconflowCheckbox = document.getElementById('use-siliconflow');
  const customPromptTextarea = document.getElementById('custom-prompt');
  const commentsPagesSlider = document.getElementById('comments-pages');
  const pagesDisplaySpan = document.getElementById('pages-display');
  const modelSelect = document.getElementById('model-select');
  const customModelInput = document.querySelector('.custom-model-input');
  const customModelIdInput = document.getElementById('custom-model-id');
  const copyResultButton = document.getElementById('copy-result');
  const copySuccess = document.getElementById('copy-success');
  const exportCsvButton = document.getElementById('export-csv');
  const exportSuccess = document.getElementById('export-success');

  // 存储当前页面URL（不显示但用于分析）
  let currentPageUrl = '';
  let lastAnalysisResult = ''; // 保存最近的分析结果，用于复制
  let lastCommentsData = null; // 保存最近的评论数据，用于导出CSV

  // 复制按钮处理
  copyResultButton.addEventListener('click', function() {
    if (lastAnalysisResult) {
      navigator.clipboard.writeText(lastAnalysisResult).then(function() {
        // 显示复制成功提示
        copySuccess.classList.add('show');
        setTimeout(() => {
          copySuccess.classList.remove('show');
        }, 2000);
      })
      .catch(function(err) {
        console.error('复制失败:', err);
      });
    }
  });

  // 将Markdown文本转换为HTML
  function markdownToHtml(markdown) {
    if (!markdown) return '';
    
    // 基本的Markdown转换
    let html = markdown
      // 处理标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      
      // 处理加粗和斜体
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      
      // 处理列表
      .replace(/^\s*\d+\.\s+(.*$)/gim, '<li>$1</li>')
      .replace(/^\s*[-*]\s+(.*$)/gim, '<li>$1</li>')
      
      // 处理引用
      .replace(/^\>\s*(.*$)/gim, '<blockquote>$1</blockquote>')
      
      // 处理代码
      .replace(/`(.*?)`/gim, '<code>$1</code>')
      
      // 处理段落，注意这应该在最后处理
      .replace(/\n\s*\n/gim, '</p><p>')
      
      // 包装在段落中
      .replace(/^(.+)$/gim, function(match) {
        // 如果不是以HTML标签开头的行，将其包装在段落中
        if (!/^<(\w+)>/.test(match)) {
          return '<p>' + match + '</p>';
        }
        return match;
      });
    
    // 将连续的li元素包装在ul中
    html = html.replace(/<li>.*?<\/li>(\s*<li>.*?<\/li>)*/g, function(match) {
      return '<ul>' + match + '</ul>';
    });
    
    return html;
  }

  // 加载存储的设置
  chrome.storage.local.get(['apiKey', 'useSiliconflow', 'customPrompt', 'commentsPages', 'modelId', 'customModelId'], function(data) {
    if (data.apiKey) {
      apiKeyInput.value = data.apiKey;
      apiStatus.textContent = '已设置API密钥';
      apiStatus.style.color = '#00B51D';
    }
    
    // 加载自定义提示词
    if (data.customPrompt) {
      customPromptTextarea.value = data.customPrompt;
    }
    
    // 加载评论页数设置
    if (data.commentsPages) {
      commentsPagesSlider.value = data.commentsPages;
      pagesDisplaySpan.textContent = `${data.commentsPages}页`;
    }
    
    // 加载模型设置
    if (data.modelId) {
      if (data.modelId === 'custom' && data.customModelId) {
        modelSelect.value = 'custom';
        customModelIdInput.value = data.customModelId;
        customModelInput.classList.remove('hidden');
      } else {
        // 尝试在选择框中找到对应选项
        const optionExists = Array.from(modelSelect.options).some(option => option.value === data.modelId);
        if (optionExists) {
          modelSelect.value = data.modelId;
        } else {
          // 如果找不到匹配的选项，设置为自定义并填入值
          modelSelect.value = 'custom';
          customModelIdInput.value = data.modelId;
          customModelInput.classList.remove('hidden');
        }
      }
    }
    
    // 如果有保存的硅基流动API设置则使用它，否则默认为true
    if (data.useSiliconflow !== undefined) {
      useSiliconflowCheckbox.checked = data.useSiliconflow;
    }
  });

  // 模型选择事件
  modelSelect.addEventListener('change', function() {
    if (this.value === 'custom') {
      customModelInput.classList.remove('hidden');
    } else {
      customModelInput.classList.add('hidden');
    }
  });

  // 评论页数滑动条事件
  commentsPagesSlider.addEventListener('input', function() {
    pagesDisplaySpan.textContent = `${this.value}页`;
    // 保存设置
    chrome.storage.local.set({ commentsPages: parseInt(this.value) });
  });

  // 获取当前标签页信息
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentUrl = tabs[0].url;
    currentPageUrl = currentUrl; // 保存URL但不显示
    
    if (currentUrl.includes('douban.com')) {
      // 尝试从页面标题中获取作品名
      let title = tabs[0].title.replace(' (豆瓣)', '').trim();
      currentTitleSpan.textContent = title;
      
      // 检测当前页面是否为电影或图书详情页
      const movieMatch = currentUrl.match(/movie\.douban\.com\/subject\/(\d+)/);
      const bookMatch = currentUrl.match(/book\.douban\.com\/subject\/(\d+)/);
      
      if (movieMatch) {
        itemTypeSelect.value = 'movie';
        itemIdInput.value = movieMatch[1];
      } else if (bookMatch) {
        itemTypeSelect.value = 'book';
        itemIdInput.value = bookMatch[1];
      }
    }
  });

  // 自动填充按钮
  autoFillButton.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      try {
        chrome.tabs.sendMessage(tabs[0].id, {action: "getPageInfo"}, function(response) {
          // 检查是否发生错误
          if (chrome.runtime.lastError) {
            console.log('自动填充出错:', chrome.runtime.lastError.message);
            // 显示错误提示，但不阻止继续操作
            currentTitleSpan.textContent = '请在豆瓣电影/图书页面使用';
            currentTitleSpan.style.color = '#e53e3e';
            setTimeout(() => {
              currentTitleSpan.style.color = '#666';
            }, 3000);
            return;
          }
          
          if (response && response.itemId) {
            itemIdInput.value = response.itemId;
            itemTypeSelect.value = response.itemType;
            
            // 如果有标题则显示
            if (response.title) {
              currentTitleSpan.textContent = response.title;
            }
            
            // 保存URL但不显示
            if (response.url) {
              currentPageUrl = response.url;
            }
          }
        });
      } catch (error) {
        console.error('自动填充错误:', error);
      }
    });
  });

  // 导出CSV按钮处理
  exportCsvButton.addEventListener('click', function() {
    if (!lastCommentsData) {
      console.error('没有可导出的评论数据');
      alert('没有可导出的评论数据，请先进行分析');
      return;
    }
    
    console.log('准备导出CSV，评论数据:', lastCommentsData);
    
    // 发送消息给background.js生成CSV
    chrome.runtime.sendMessage({
      action: 'exportCommentsCSV',
      data: {
        commentsData: lastCommentsData
      }
    }, function(response) {
      if (chrome.runtime.lastError) {
        console.error('导出CSV出错:', chrome.runtime.lastError);
        alert('导出失败: ' + chrome.runtime.lastError.message);
        return;
      }
      
      console.log('收到CSV导出响应:', response);
      
      if (response && response.success) {
        // 创建Blob对象用于下载，指定正确的MIME类型和编码
        const blob = new Blob([response.csvContent], { 
          type: 'text/csv;charset=utf-8'
        });
        const url = URL.createObjectURL(blob);
        
        // 创建一个虚拟链接并触发下载
        const link = document.createElement('a');
        link.href = url;
        link.download = response.fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        
        // 清理
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          
          // 显示导出成功提示
          exportSuccess.classList.add('show');
          setTimeout(() => {
            exportSuccess.classList.remove('show');
          }, 2000);
        }, 100);
      } else {
        console.error('导出CSV失败:', response ? response.error : '未知错误');
        alert('导出失败: ' + (response ? response.error : '未知错误'));
      }
    });
  });

  // 分析按钮
  analyzeButton.addEventListener('click', function() {
    const itemId = itemIdInput.value;
    const itemType = itemTypeSelect.value;
    const customPrompt = customPromptTextarea.value.trim();
    const maxCommentsPages = parseInt(commentsPagesSlider.value);
    
    if (!itemId) {
      resultContainer.innerHTML = '<p class="error">请填写电影/图书ID</p>';
      return;
    }
    
    // 显示加载状态
    analyzeButton.disabled = true;
    analyzeButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';
    resultContainer.innerHTML = '<p>正在获取和分析评论，请稍候...</p>';
    
    // 重置CSV导出按钮
    exportCsvButton.disabled = true;

    // 获取当前选择的模型ID
    let modelId;
    if (modelSelect.value === 'custom') {
      modelId = customModelIdInput.value;
    } else {
      modelId = modelSelect.value;
    }
    
    // 发送消息到 background.js 进行处理
    chrome.runtime.sendMessage({
      action: 'analyzeComments',
      data: {
        itemId: itemId,
        itemType: itemType,
        customPrompt: customPrompt,
        maxCommentsPages: maxCommentsPages,
        modelId: modelId
      }
    }, function(response) {
      analyzeButton.disabled = false;
      analyzeButton.innerHTML = '<i class="fas fa-chart-pie"></i> 开始分析';
      
      if (response.success) {
        // 保存分析结果用于复制
        lastAnalysisResult = response.result;
        
        // 保存评论数据用于导出CSV
        if (response.commentsData) {
          lastCommentsData = response.commentsData;
          // 启用CSV导出按钮
          exportCsvButton.disabled = false;
        }
        
        // 直接在界面上显示评论统计信息
        let statsHtml = '';
        if (response.stats) {
          const { totalComments, pagesRetrieved, settingPages } = response.stats;
          statsHtml = `
            <div class="comments-stats">
              <strong>评论统计:</strong> 已获取${totalComments}条评论（共${pagesRetrieved}页）
              ${pagesRetrieved < settingPages ? 
                `<div class="note-info">注意：设置了${settingPages}页，但豆瓣仅允许获取${pagesRetrieved}页</div>` : 
                ''}
            </div>`;
        } else {
          statsHtml = `<div class="comments-stats">已分析评论：查看开发者工具控制台可见获取详情</div>`;
        }
        
        // 转换Markdown格式
        const htmlResult = markdownToHtml(response.result);
        
        // 更新UI，保留复制按钮
        resultContainer.innerHTML = `
          <button id="copy-result" class="copy-button" title="复制结果">
            <div class="copy-icon"></div>
          </button>
          <div id="copy-success" class="copy-success">已复制!</div>
          ${statsHtml}
          <div class="analysis-result">${htmlResult}</div>
        `;
        
        // 重新绑定复制按钮事件
        document.getElementById('copy-result').addEventListener('click', function() {
          if (lastAnalysisResult) {
            navigator.clipboard.writeText(lastAnalysisResult).then(function() {
              // 显示复制成功提示
              document.getElementById('copy-success').classList.add('show');
              setTimeout(() => {
                document.getElementById('copy-success').classList.remove('show');
              }, 2000);
            })
            .catch(function(err) {
              console.error('复制失败:', err);
            });
          }
        });
        
        // 提示用户查看控制台
        console.log('------- 豆瓣评论分析器报告 -------');
        console.log(`分析完成：${itemType === 'movie' ? '电影' : '图书'} ID: ${itemId}`);
        console.log(`设置的评论页数: ${maxCommentsPages}页`);
        if (response.stats) {
          console.log(`实际获取: ${response.stats.totalComments}条评论，${response.stats.pagesRetrieved}页`);
        }
        console.log('详细的评论获取情况请查看以上日志');
      } else {
        resultContainer.innerHTML = `
          <button id="copy-result" class="copy-button" title="复制结果">
            <div class="copy-icon"></div>
          </button>
          <div id="copy-success" class="copy-success">已复制!</div>
          <p class="error">分析失败: ${response.error}</p>
        `;
        lastAnalysisResult = `分析失败: ${response.error}`;
        // 禁用CSV导出按钮
        exportCsvButton.disabled = true;
      }
    });
  });

  // 设置面板切换
  settingsToggle.addEventListener('click', function() {
    settingsPanel.classList.toggle('hidden');
  });

  // 保存API密钥
  saveApiKeyButton.addEventListener('click', function() {
    const apiKey = apiKeyInput.value.trim();
    const useSiliconflow = useSiliconflowCheckbox.checked;
    const customPrompt = customPromptTextarea.value.trim(); // 获取自定义提示词

    // 获取模型ID
    let modelId;
    let customModelId = '';

    if (modelSelect.value === 'custom') {
      modelId = 'custom';
      customModelId = customModelIdInput.value.trim();
      if (!customModelId) {
        apiStatus.textContent = '请输入有效的自定义模型ID';
        apiStatus.style.color = '#E53E3E';
        return;
      }
    } else {
      modelId = modelSelect.value;
    }

    if (apiKey) {
      chrome.storage.local.set({
        apiKey: apiKey,
        useSiliconflow: useSiliconflow,
        modelId: modelId,
        customModelId: customModelId,
        customPrompt: customPrompt // 保存自定义提示词
      }, function() {
        apiStatus.textContent = '已保存设置';
        apiStatus.style.color = '#00B51D';

        // 3秒后恢复
        setTimeout(function() {
          apiStatus.textContent = '已设置API密钥';
        }, 3000);
      });
    } else {
      apiStatus.textContent = '请输入有效的API密钥';
      apiStatus.style.color = '#E53E3E';
    }
  });
}); 