<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            text-align: center;
            background-color: #f0f8ff;
        }
        button {
            padding: 10px 20px;
            background-color: #4a6cf7;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .icon-display {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }
        .icon-item {
            margin: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>日期天气卡片图标生成器</h1>
    <p>点击按钮生成所需的图标文件</p>
    
    <div class="icon-display">
        <div class="icon-item">
            <canvas id="canvas16" width="16" height="16"></canvas>
            <div>16x16</div>
        </div>
        <div class="icon-item">
            <canvas id="canvas48" width="48" height="48"></canvas>
            <div>48x48</div>
        </div>
        <div class="icon-item">
            <canvas id="canvas128" width="128" height="128"></canvas>
            <div>128x128</div>
        </div>
    </div>
    
    <div>
        <button id="generateBtn">一键生成所有图标</button>
    </div>
    
    <script>
        // 获取DOM元素
        const canvas16 = document.getElementById('canvas16');
        const canvas48 = document.getElementById('canvas48');
        const canvas128 = document.getElementById('canvas128');
        const generateBtn = document.getElementById('generateBtn');
        
        // 绘制图标函数
        function drawIcon(canvas) {
            const ctx = canvas.getContext('2d');
            const size = canvas.width;
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size / 2;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4a6cf7');
            gradient.addColorStop(1, '#6a11cb');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制日期数字
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 根据尺寸调整字体大小
            const fontSize = Math.floor(size * 0.5);
            ctx.font = `bold ${fontSize}px Arial`;
            
            // 获取当前日期
            const day = new Date().getDate();
            ctx.fillText(day.toString(), centerX, centerY);
        }
        
        // 下载图标
        function downloadIcon(canvas, size) {
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // 生成所有图标
        function generateIcons() {
            // 绘制图标
            drawIcon(canvas16);
            drawIcon(canvas48);
            drawIcon(canvas128);
            
            // 下载图标
            downloadIcon(canvas16, 16);
            downloadIcon(canvas48, 48);
            downloadIcon(canvas128, 128);
        }
        
        // 初始化显示
        drawIcon(canvas16);
        drawIcon(canvas48);
        drawIcon(canvas128);
        
        // 添加按钮点击事件
        generateBtn.addEventListener('click', generateIcons);
    </script>
</body>
</html> 