import apiConfigs from './apiConfig.js';

let currentContent = '';
let conversationHistory = [];
let currentTabId = null;
let savedResult = '';
let noteCounter = 0;
let currentUrl = '';
let clickCount = 0;

// 使用缓存
let responseCache = {};

// 在文件顶部添加或修改以下代码
let currentPlatform = '';
let apiKeys = {};

// 在文件顶部添加语言配置
const languages = {
  en: {
    title: "Zen Sticky Notes, Are you OK",
    summarize: "I'm OK",
    ask: "Ask",
    settings: "Settings",
    clear: "Clear",
    copy: "Copy",
    export: "Export Notes", // 添加导出按钮的文本
    apiPlatform: "API Platform",
    apiKey: "API Key",
    model: "Model",
    baseUrl: "Base URL",
    saveSettings: "Save Settings",
    settingsSaved: "Settings saved successfully",
    settingsError: "Error saving settings",
    confirmOverwrite: "There is a saved result. Do you want to overwrite it?",
    summarizePrompt: "Please create a complete summary of the following article in no more than 800 words. Requirements: 1)Cover all main points and key information 2)Maintain logical completeness 3)Use concise language 4)Include all important points if multiple exist 5)Ensure the summary is complete without missing information",
    characters: "characters",
    summary: "Summary:",
    baseContent: "Based on the following content",
    question: "Question",
    noContent: "Unable to retrieve page content",
    fontSize: "Font Size",
    small: "Small",
    medium: "Medium",
    large: "Large",
    processing: "Processing...",
    dailyOkTitle: "Daily I'm OK 100+ times",
    becomeOkPerson: "You'll become a very OK person",
    okConfidenceIndex: "OK Confidence Index",
    noExportContent: "No notes to export",
    clearNotes: "Clear Notes",
    statusDisplay: "Show Status Bar"
  },
  zh: {
    title: "智能便利贴, Are you OK",
    summarize: "I'm OK",
    ask: "提问",
    settings: "设置",
    clear: "清除",
    copy: "复制",
    export: "导出笔记", // 添加导出按钮的文本
    apiPlatform: "API 平台",
    apiKey: "API 密钥",
    model: "模型",
    baseUrl: "基础 URL",
    saveSettings: "保存设置",
    settingsSaved: "设置保存成功",
    settingsError: "保存设置时出错",
    confirmOverwrite: "已有保存的结果，是否要覆盖？",
    summarizePrompt: "请用800字以内总结以下文章的全部内容。必须包含所有要点，不能遗漏任何重要信息。如果原文有5个解决方案，总结中必须包含全部5个。要求简洁但完整，逻辑清晰。字数限制：最多800字",
    characters: "字符",
    summary: "总结主要内容如下：",
    baseContent: "基于以下内容",
    question: "问题",
    noContent: "无法获取页面内容",
    fontSize: "字体大小",
    small: "小",
    medium: "中",
    large: "大",
    processing: "处理中...",
    dailyOkTitle: "每天 I'm OK 100+ 次",
    becomeOkPerson: "你会变得很OK",
    okConfidenceIndex: "OK信心指数",
    noExportContent: "没有笔记可导出",
    clearNotes: "清空记录",
    statusDisplay: "显示状态栏"
  },
  zh_tw: {
    title: "智慧便利貼, Are you OK",
    summarize: "I'm OK",
    ask: "提問",
    settings: "設置",
    clear: "清除",
    copy: "複製",
    export: "導出筆記", // 添加导出按钮的文本
    apiPlatform: "API 平台",
    apiKey: "API 密鑰",
    model: "模型",
    baseUrl: "基礎 URL",
    saveSettings: "保存設置",
    settingsSaved: "設置保存成功",
    settingsError: "保存設置時出錯",
    confirmOverwrite: "已有儲存的結果，是否要覆蓋？",
    summarizePrompt: "請將以下文章總結為一篇不超過800字的完整總結。要求：1)涵蓋所有主要觀點和關鍵信息 2)保持邏輯完整性 3)語言簡潔精煉 4)如有多個要點請全部包含 5)確保總結完整且信息不遺漏",
    characters: "字元",
    summary: "總結主要內容如下：",
    baseContent: "基於以下內容",
    question: "問題",
    noContent: "無法獲取頁面內容",
    fontSize: "字體大小",
    small: "小",
    medium: "中",
    large: "大",
    processing: "處理中...",
    dailyOkTitle: "每天 I'm OK 100+ 次",
    becomeOkPerson: "你會變得很OK",
    okConfidenceIndex: "OK信心指數",
    noExportContent: "沒有筆記可導出",
    clearNotes: "清空記錄",
    statusDisplay: "顯示狀態欄"
  }
};

let currentLanguage = 'zh';
let showStatusDisplay = true; // 默认显示状态信息

function updatePlatformOptions() {
  const platformSelect = document.getElementById('apiPlatform');
  platformSelect.innerHTML = '';
  Object.keys(apiConfigs).forEach(platform => {
    const option = document.createElement('option');
    option.value = platform;
    option.textContent = apiConfigs[platform].name;
    platformSelect.appendChild(option);
    if (platform === currentPlatform) {
      option.selected = true;
    }
  });
  updateModelOptions();
  
  // 控制 API 密钥输入框和鱼眼图标的显示/隐藏
  const apiKeyInput = document.getElementById('apiKey');
  const toggleApiKeyVisibilityBtn = document.getElementById('toggleApiKeyVisibility');
  if (currentPlatform === 'ollama') {
    apiKeyInput.style.display = 'none';
    toggleApiKeyVisibilityBtn.style.display = 'none'; // 隐藏鱼眼图标
  } else {
    apiKeyInput.style.display = 'block';
    toggleApiKeyVisibilityBtn.style.display = 'inline-block'; // 显示鱼眼图标
  }
}

function updateModelOptions() {
  const modelSelect = document.getElementById('modelSelect');
  modelSelect.innerHTML = '';
  apiConfigs[currentPlatform].models.forEach(model => {
    const option = document.createElement('option');
    option.value = model.id;
    option.textContent = model.name;
    modelSelect.appendChild(option);
  });
  toggleCustomModelInput();
  updateBaseUrl();
}

function updateBaseUrl() {
  const baseUrlInput = document.getElementById('baseUrl');
  baseUrlInput.value = apiConfigs[currentPlatform].baseUrl;
}

document.addEventListener('DOMContentLoaded', function() {
  loadSettings();

  // 加载上次的结果
  chrome.storage.local.get(['lastResult'], function(result) {
    if (result.lastResult) {
      savedResult = result.lastResult;
      const htmlContent = markdownToHtml(savedResult);
      document.getElementById('result').innerHTML = htmlContent;
      document.getElementById('copyBtn').style.display = 'block';
    }
  });
    
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    if (tabs[0]) {
      if (tabs[0].url !== currentUrl) {
        // 只清空输入框，不清空结果
        document.getElementById('customPrompt').value = '';
        currentUrl = tabs[0].url;
      }
      currentTabId = tabs[0].id;
      getPageContent(tabs[0].id);
    }
  });
    
  document.getElementById('summarizeBtn').addEventListener('click', () => processContent('summarize'));
  document.getElementById('askBtn').addEventListener('click', askCustomQuestion);
  document.getElementById('copyBtn').addEventListener('click', copyResult);
  document.getElementById('exportBtn').addEventListener('click', exportNotes); // 添加导出按钮事件监听器
  document.getElementById('settingsBtn').addEventListener('click', toggleSettings);
  // 确保设置按钮显示齿轮图标
  document.getElementById('settingsBtn').innerHTML = '⚙️';
  document.getElementById('saveSettings').addEventListener('click', saveSettings);
  document.getElementById('clearBtn').addEventListener('click', clearAll);
  document.getElementById('clearNotesBtn').addEventListener('click', clearNotes);
  document.getElementById('statusDisplayToggle').addEventListener('change', toggleStatusDisplay);
  document.getElementById('modelSelect').addEventListener('change', toggleCustomModelInput);
  document.getElementById('apiPlatform').addEventListener('change', function() {
    currentPlatform = this.value;
    document.getElementById('apiKey').value = apiKeys[currentPlatform] || '';
    updateModelOptions();
    console.log('Platform changed to:', currentPlatform);
    // API密钥信息已移除，避免控制台泄露
    
    // 控制 API 密钥输入框和鱼眼图标的显示/隐藏
    const apiKeyInput = document.getElementById('apiKey');
    const toggleApiKeyVisibilityBtn = document.getElementById('toggleApiKeyVisibility');
    if (currentPlatform === 'ollama') {
      apiKeyInput.style.display = 'none';
      toggleApiKeyVisibilityBtn.style.display = 'none'; // 隐藏鱼眼图标
    } else {
      apiKeyInput.style.display = 'block';
      toggleApiKeyVisibilityBtn.style.display = 'inline-block'; // 显示鱼眼图标
    }
  });

  // 加载笔记计数器
  chrome.storage.local.get(['noteCounter'], function(result) {
    noteCounter = result.noteCounter || 0;
  });
    
  loadClickCount();
  document.getElementById('summarizeBtn').addEventListener('click', incrementClickCount);
  document.getElementById('askBtn').addEventListener('click', incrementClickCount);

  const themeSwitcher = document.getElementById('themeSwitcher');
  themeSwitcher.addEventListener('click', switchTheme);

  // 加载保存的主题
  chrome.storage.sync.get(['currentTheme'], function(result) {
    if (result.currentTheme !== undefined) {
      currentThemeIndex = result.currentTheme;
      switchTheme();
    }
  });

  // 添加 API Key 输入框的事件监听器
  document.getElementById('apiKey').addEventListener('input', function() {
    const apiKey = this.value.trim();
    if (apiKey) {
      apiKeys[currentPlatform] = apiKey;
    } else {
      delete apiKeys[currentPlatform];
    }
    // API密钥更新日志已移除，避免控制台泄露
  });

  document.getElementById('languageSelect').addEventListener('change', function() {
    currentLanguage = this.value;
    updateLanguage();
    chrome.storage.sync.set({language: currentLanguage});
    console.log('Language changed to:', currentLanguage);  // 添加日志
  });

  // 加载保存的语言设置
  chrome.storage.sync.get(['language'], function(result) {
    if (result.language) {
      currentLanguage = result.language;
      document.getElementById('languageSelect').value = currentLanguage;
    }
    updateLanguage();
  });

  // 添加字体大小选择器的事件监听器
  document.getElementById('fontSizeSelect').addEventListener('change', function() {
    const fontSize = this.value;
    updateFontSize(fontSize);
    chrome.storage.sync.set({fontSize: fontSize});
    console.log('Font size changed to:', fontSize);
  });

  // 加载保存的字体大小设置
  chrome.storage.sync.get(['fontSize'], function(result) {
    if (result.fontSize) {
      document.getElementById('fontSizeSelect').value = result.fontSize;
      updateFontSize(result.fontSize);
    }
  });

  updatePlatformOptions();

  // 在这里也调用新函数
  setupApiKeyToggle();

  // 修改事件监听器，使其在设置更改时自动保存
  document.getElementById('apiPlatform').addEventListener('change', saveSettings);
  document.getElementById('apiKey').addEventListener('input', saveSettings);
  document.getElementById('modelSelect').addEventListener('change', saveSettings);
  document.getElementById('customModel').addEventListener('input', saveSettings);
  document.getElementById('languageSelect').addEventListener('change', function() {
    currentLanguage = this.value;
    saveSettings();
    updateLanguage();
  });
  document.getElementById('fontSizeSelect').addEventListener('change', function() {
    saveSettings();
    updateFontSize(this.value);
  });

  // 移除保存设置按钮的事件监听器，因为我们不再需要它
  // document.getElementById('saveSettings').removeEventListener('click', saveSettings);
});

function getPageContent(tabId, callback) {
  chrome.scripting.executeScript({
    target: {tabId: tabId},
    function: () => {
      const contentElements = document.querySelectorAll('article, .article, .content, .main, main, #main, #content');
      if (contentElements.length > 0) {
        return Array.from(contentElements).map(el => el.innerText).join('\n\n');
      }
      return document.body.innerText;
    }
  }, (results) => {
    if (results && results[0] && results[0].result) {
      currentContent = results[0].result;
      console.log('Retrieved content length:', currentContent.length);
    } else {
      console.error('Failed to get page content');
    }
    // 执行回调函数（如果提供）
    if (callback && typeof callback === 'function') {
      callback();
    }
  });
}

function getPageTitle(tabId, callback) {
  chrome.scripting.executeScript({
    target: {tabId: tabId},
    function: () => document.title
  }, (results) => {
    if (results && results[0] && results[0].result) {
      callback(results[0].result);
    } else {
      callback('未知标题');
    }
  });
}

function processContent(action, customPrompt = '') {
  // 实时检测当前标签页URL变化
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    if (tabs[0] && tabs[0].url !== currentUrl) {
      // 检测到新页面，更新URL和内容
      currentUrl = tabs[0].url;
      currentTabId = tabs[0].id;
      getPageContent(tabs[0].id, () => {
        // 内容更新后继续执行
        executeProcessContent(action, customPrompt);
      });
      return;
    }
    // 同一页面，直接执行
    executeProcessContent(action, customPrompt);
  });
}

function executeProcessContent(action, customPrompt = '') {
  console.log('Current language:', currentLanguage);  // 添加日志
  incrementClickCount();

  if (currentContent) {
    let prompt;
    let questionOrSummary;
    let languagePrompt = getLanguagePrompt();

    if (action === 'summarize') {
      prompt = `${languagePrompt}\n\n${languages[currentLanguage].summarizePrompt} (${currentContent.length} ${languages[currentLanguage].characters}):\n\n${truncateContent(currentContent, 40000)}\n\n${languagePrompt}\n\n重要提醒：总结必须在800字以内完成，包含所有要点后立即结束，不要添加"文章被截断"等说明。`;
      questionOrSummary = languages[currentLanguage].summary;
    } else if (action === 'ask' || action === 'continue') {
      prompt = `${languagePrompt}\n\n${languages[currentLanguage].baseContent}:\n\n${truncateContent(currentContent, 40000)}\n\n${languages[currentLanguage].question}: ${customPrompt}\n\n${languagePrompt}`;
      questionOrSummary = `${languages[currentLanguage].question}: ${customPrompt}`;
    }

    const cacheKey = `${action}_${currentUrl}_${prompt}`;
    if (responseCache[cacheKey]) {
      displayResult(responseCache[cacheKey], questionOrSummary);
      return;
    }

    // 提示词日志已移除，避免敏感内容泄露
    callModelAPI(prompt, action === 'continue', action === 'ask', questionOrSummary);
  } else {
    document.getElementById('result').innerText = languages[currentLanguage].noContent;
  }
}

function getLanguagePrompt() {
  switch (currentLanguage) {
    case 'en':
      return "IMPORTANT: Please respond in English only. Do not use any other language.";
    case 'zh':
      return "重要提示：请只用简体中文回答。不要使用任何其他语言。";
    case 'zh_tw':
      return "重要提示：請只用繁體中文回答。不要使用任何其他語言。";
    default:
      return "IMPORTANT: Please respond in English only. Do not use any other language.";
  }
}

async function callModelAPI(prompt, isContinuation = false, isQuestion = false, questionOrSummary) {
  const apiPlatform = document.getElementById('apiPlatform').value;
  const modelSelect = document.getElementById('modelSelect');
  const customModelInput = document.getElementById('customModel');
  let modelId = modelSelect.value;
  
  if (modelId === 'custom') {
    modelId = customModelInput.value;
  }
  
  const apiKey = apiPlatform !== 'ollama' ? document.getElementById('apiKey').value.trim() : null;
  const baseUrl = apiConfigs[apiPlatform].baseUrl;

  if (apiPlatform !== 'ollama' && !apiKey) {
    document.getElementById('result').innerText = '请在设置中输入API Key';
    return;
  }

  document.getElementById('result').innerText = languages[currentLanguage].processing;

  let messages;
  if (isContinuation) {
    messages = [...conversationHistory, {role: "user", content: prompt}];
  } else if (isQuestion) {
    messages = [{role: "system", content: `以下是网页内容：\n\n${currentContent}`}, {role: "user", content: prompt}];
  } else {
    messages = [{role: "user", content: prompt}];
  }

  console.log('API Platform:', apiPlatform);
  console.log('Model ID:', modelId);
  console.log('Base URL:', baseUrl);

  try {
    let response;
    if (apiPlatform === 'gemini') {
      console.log('Calling Gemini API with model:', modelId);
      // 构建Gemini API请求
      const fullPrompt = messages.map(msg => {
        return {
          role: msg.role === "system" ? "user" : msg.role,
          parts: [{ text: msg.content }]
        };
      });
      
      const requestBody = {
        contents: fullPrompt,
        generationConfig: {
          temperature: 0.1,
          topK: 20,
          topP: 0.8,
          maxOutputTokens: 1200,
          candidateCount: 1,
          stopSequences: ["（文章在此处被截断", "文章内容在此处被截断", "...（内容被截断"],
        }
      };
      
      // Gemini API端点
      const apiUrl = `${baseUrl}/${modelId}:generateContent?key=${apiKey}`;
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      const rawResponse = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      
      if (!rawResponse.ok) {
        const errorText = await rawResponse.text();
        throw new Error(`HTTP error! status: ${rawResponse.status}, message: ${errorText}`);
      }
      
      response = await rawResponse.json();
    } else if (apiPlatform === 'openai') {
      console.log('Calling OpenAI API with model:', modelId);
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      };
      const body = {
        model: modelId,
        messages: messages,
        temperature: 0.3
      };
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      const rawResponse = await fetch(`${baseUrl}/chat/completions`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      
      if (!rawResponse.ok) {
        const errorText = await rawResponse.text();
        throw new Error(`HTTP error! status: ${rawResponse.status}, message: ${errorText}`);
      }
      
      response = await rawResponse.json();
    } else if (apiPlatform === 'ollama') {
      // Ollama 的处理，确保模型名称没有多余空格
      const cleanModelId = modelId.replace(/\s+/g, '');
      console.log('Ollama 原始模型名称:', modelId);
      console.log('Ollama 处理后模型名称:', cleanModelId);
      
      const data = {
        model: cleanModelId,
        prompt: prompt,
        stream: false
      };
      
      console.log('Ollama 请求数据:', JSON.stringify(data));
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      response = await fetch(`${baseUrl}/api/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
        signal: controller.signal
      }).then(res => res.json());

      clearTimeout(timeoutId);
      console.log('Ollama 原始响应:', response);
    } else if (apiPlatform === 'siliconflow') {
      console.log('Calling SiliconFlow API with model:', modelId);
      
      const url = "https://api.siliconflow.cn/v1/chat/completions";
      const payload = {
        "model": modelId,
        "messages": messages,
        "stream": false,
        "max_tokens": 1024,
        "temperature": 0.7,
        "top_p": 0.7
      };

      try {
        console.log("开始发送SiliconFlow API请求...");
        console.log("使用模型:", modelId);
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

        const apiResponse = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify(payload),
          signal: controller.signal
        });

        clearTimeout(timeoutId);
    
        if (!apiResponse.ok) {
          const errorText = await apiResponse.text().catch(() => "无法获取错误详情");
          console.error("API响应错误:", apiResponse.status, errorText);
          throw new Error(`SiliconFlow API请求失败: ${apiResponse.status} ${apiResponse.statusText}`);
        }
    
        response = await apiResponse.json();
        console.log("SiliconFlow API响应:", response);
      } catch (error) {
        console.error("SiliconFlow API请求错误:", error);
        if (error.message.includes('Failed to fetch')) {
          throw new Error('SiliconFlow API请求失败，请检查您的网络连接或API服务是否可用');
        }
        throw error;
      }
    } else {
      // 其他平台的处理
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      };
      if (apiPlatform === 'openrouter') {
        headers['HTTP-Referer'] = chrome.runtime.getURL('popup.html');
        headers['X-Title'] = 'Web Content Summarizer';
      }
      const body = {
        model: modelId,
        messages: messages,
        temperature: 0.3
      };
      if (apiPlatform === 'openrouter') {
        body.transforms = ["middle-out"];
      }
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      response = await fetch(`${baseUrl}/chat/completions`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body),
        signal: controller.signal
      }).then(res => res.json());

      clearTimeout(timeoutId);
    }

    // API响应日志已移除，避免敏感内容泄露

    let result;
    if (apiPlatform === 'openai' || apiPlatform === 'moonshot' || apiPlatform === 'openrouter' || apiPlatform === 'siliconflow') {
      if (response.choices && response.choices.length > 0 && response.choices[0].message) {
        result = response.choices[0].message.content;
        const finishReason = response.choices[0].finish_reason;
        const statusInfo = `📊 ${apiPlatform.toUpperCase()}状态: ${finishReason || 'UNKNOWN'} | 📏 文本长度: ${result.length}字 | 🤖 模型: ${modelId}`;
        showStatus(statusInfo);

        if (finishReason && finishReason !== 'stop') {
          const warningInfo = `⚠️ 响应被截断 - 原因: ${finishReason} | 📏 已生成: ${result.length}字`;
          showStatus(warningInfo);
        }
      } else {
        throw new Error('API响应格式错误，请检查网络连接或API配置');
      }
    } else if (apiPlatform === 'ollama') {
      if (response.response) {
        result = response.response;
      } else {
        throw new Error('Ollama API响应格式错误，请检查Ollama服务是否正常运行');
      }
    } else if (apiPlatform === 'gemini') {
      // 处理Gemini API响应
      console.log('Gemini response candidates:', response.candidates);
      if (response.candidates &&
          response.candidates[0] &&
          response.candidates[0].content &&
          response.candidates[0].content.parts) {

        // 检查finishReason
        const finishReason = response.candidates[0].finishReason;
        console.log('Gemini finishReason:', finishReason);

        // 从parts数组中提取文本
        let generatedText = "";
        for (const part of response.candidates[0].content.parts) {
          if (part.text) {
            generatedText += part.text;
          }
        }
        result = generatedText;
        console.log('Gemini生成文本长度:', result.length);

        // 清理不完整的结尾
        result = cleanIncompleteEnding(result);

        // 显示状态信息
        const usageMetadata = response.usageMetadata;
        console.log('Gemini usageMetadata:', usageMetadata);
        const promptTokens = usageMetadata ? usageMetadata.promptTokenCount : 'N/A';
        const outputTokens = usageMetadata ? usageMetadata.candidatesTokenCount : 'N/A';
        const totalTokens = usageMetadata ? usageMetadata.totalTokenCount : 'N/A';

        let statusInfo;
        const isLengthExceeded = result.length > 1000;
        const lengthWarning = isLengthExceeded ? ' ⚠️偏长' : '';

        if (usageMetadata) {
          statusInfo = `📊 Gemini状态: ${finishReason || 'UNKNOWN'} | 📏 文本长度: ${result.length}字${lengthWarning} | 🤖 模型: ${modelId}<br>📝 输入tokens: ${promptTokens} | 📤 输出tokens: ${outputTokens} | 📊 总计: ${totalTokens}`;
        } else {
          statusInfo = `📊 Gemini状态: ${finishReason || 'UNKNOWN'} | 📏 文本长度: ${result.length}字${lengthWarning} | 🤖 模型: ${modelId}<br>⚠️ 无法获取token使用信息`;
        }
        showStatus(statusInfo);

        if (finishReason && finishReason !== 'STOP') {
          console.warn('Gemini响应可能被截断，原因:', finishReason);
          const warningInfo = `⚠️ 响应被截断 - 原因: ${finishReason} | 📏 已生成: ${result.length}字 | 📤 输出tokens: ${outputTokens}`;
          showStatus(warningInfo);
        }

      } else {
        throw new Error('Gemini API响应格式错误，请检查API密钥或网络连接');
      }
    } else {
      throw new Error('Unexpected API platform');
    }

    if (!result) {
      throw new Error('No result found in API response');
    }

    displayResult(result, questionOrSummary);
    responseCache[`${isContinuation ? 'continue' : (isQuestion ? 'ask' : 'summarize')}_${currentUrl}_${prompt}`] = result;
    if (isContinuation || isQuestion) {
      conversationHistory.push({role: "user", content: prompt});
      conversationHistory.push({role: "assistant", content: result});
    } else {
      conversationHistory = [{role: "user", content: prompt}, {role: "assistant", content: result}];
    }
  } catch (error) {
    console.error('Error in API call:', error);
    if (error.name === 'AbortError') {
      displayResult('请求超时，请检查网络连接或稍后重试');
    } else if (error.message.includes('Failed to fetch')) {
      displayResult('网络连接失败，请检查网络设置');
    } else {
      displayResult(`处理失败：${error.message}`);
    }
  }
}

// 简单的 Markdown 转 HTML 函数
function markdownToHtml(text) {
  // 先保护已经存在的HTML标签，避免重复处理
  let result = text
    // 先转义HTML特殊字符（除了我们要处理的markdown符号）
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');

  // 处理标题 (### -> <h3>, ## -> <h2>, # -> <h1>)
  result = result
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>');

  // 处理代码块 (`code` -> <code>code</code>) - 先处理，避免与其他格式冲突
  result = result.replace(/`([^`]+)`/g, '<code>$1</code>');

  // 处理粗体 (**text** 或 __text__ -> <strong>text</strong>)
  result = result
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/__(.*?)__/g, '<strong>$1</strong>');

  // 处理斜体 - 使用最简单兼容的方法
  // 只处理明确的单下划线斜体（空格分隔的单词）
  result = result.replace(/\s_([^_\s][^_]*?)_\s/g, ' <em>$1</em> ');
  result = result.replace(/^_([^_\s][^_]*?)_\s/gm, '<em>$1</em> ');
  result = result.replace(/\s_([^_\s][^_]*?)_$/gm, ' <em>$1</em>');

  // 处理单星号斜体，使用临时标记避免与粗体冲突
  result = result.replace(/\*([^*\s][^*]*?)\*/g, function(match, content) {
    // 简单检查：如果内容很短且不包含空格，可能是斜体
    if (content.length > 0 && !content.includes('<strong>')) {
      return '<em>' + content + '</em>';
    }
    return match;
  });

  // 处理列表
  result = result
    .replace(/^\d+\.\s+(.*$)/gm, '<li>$1</li>')
    .replace(/^[-*]\s+(.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*?<\/li>(?:\s*<li>.*?<\/li>)*)/gs, '<ul>$1</ul>');

  // 处理换行和段落
  result = result
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^(.*)$/s, '<p>$1</p>');

  // 修复嵌套的标签问题
  result = result
    .replace(/<\/ul>\s*<ul>/g, '')
    .replace(/<p><\/p>/g, '')
    .replace(/<p>(<h[1-6]>.*?<\/h[1-6]>)<\/p>/g, '$1')
    .replace(/<p>(<ul>.*?<\/ul>)<\/p>/gs, '$1');

  return result;
}

function displayResult(text, questionOrSummary) {
  const resultElement = document.getElementById('result');
  const modelId = document.getElementById('modelSelect').value;
  // 移除所有可能的前缀
  const cleanedText = text.replace(/^(总结：|总结主要内容如下：|总结如下：)\s*/, '');
  savedResult = `${questionOrSummary}\n${cleanedText}`;

  // 将 Markdown 转换为 HTML 并显示
  const htmlContent = markdownToHtml(savedResult);
  resultElement.innerHTML = htmlContent;

  document.getElementById('copyBtn').style.display = 'block';

  // 保存结果到storage，以便下次打开时恢复
  chrome.storage.local.set({ lastResult: savedResult });

  getPageTitle(currentTabId, (pageTitle) => {
    saveNoteToStorage(pageTitle, questionOrSummary, cleanedText, modelId);
  });
}

// 修改 saveNoteToStorage 函数以接收模型ID
function saveNoteToStorage(title, question, answer, modelId) {
  console.log(`Attempting to save note for title: ${title}`);
  chrome.storage.local.get([title], function(result) {
    let notes = result[title] || [];
    notes.push({ question, answer, modelId });
    chrome.storage.local.set({ [title]: notes }, function() {
      if (chrome.runtime.lastError) {
        console.error('Error saving note:', chrome.runtime.lastError);
      } else {
        console.log(`已保存笔记到标题：${title}`);
        console.log('当前笔记内容:', notes);
      }
    });
  });
}

// 新增: 导出所有笔记为一个文件
function exportNotes() {
  console.log('exportNotes function called');
  chrome.storage.local.get(null, function(result) {
    console.log('Retrieved data from storage:', result);
    if (Object.keys(result).length === 0) {
      console.log('No notes to export');
      alert(languages[currentLanguage].noExportContent || '没有笔记可导出。');
      return;
    }

    let content = '';
    for (let title in result) {
      if (title === 'lastResult' || title === 'noteCounter' || !Array.isArray(result[title])) continue;
      content += `标题：${title}\n\n`;
      result[title].forEach((entry, index) => {
        content += `问题 ${index + 1}: ${entry.question}\n答案 ${index + 1}: ${entry.answer}（${entry.modelId}）\n\n`;
      });
      content += '------------------------\n\n';
    }

    if (content === '') {
      console.log('No valid notes to export');
      alert(languages[currentLanguage].noExportContent || '没有有效的笔记可导出。');
      return;
    }

    console.log('Prepared content for export:', content);

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const filename = `OK_Notes_${new Date().toISOString().slice(0,10)}.txt`;

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('Export completed');
  });
}

// 新增: 清空所有笔记记录
function clearNotes() {
  console.log('clearNotes function called');
  if (confirm('确定要清空所有笔记记录吗？此操作不可撤销。')) {
    chrome.storage.local.get(null, function(result) {
      // 保留必要的设置项，清除所有笔记数据
      const keysToKeep = ['lastResult', 'noteCounter', 'apiKey', 'currentPlatform', 'modelId', 'customModelId', 'currentLanguage', 'fontSize'];
      const keysToRemove = [];

      for (let key in result) {
        if (!keysToKeep.includes(key)) {
          keysToRemove.push(key);
        }
      }

      if (keysToRemove.length > 0) {
        chrome.storage.local.remove(keysToRemove, function() {
          console.log('Notes cleared successfully');
          alert('所有笔记记录已清空。');
        });
      } else {
        console.log('No notes to clear');
        alert('没有笔记记录需要清空。');
      }
    });
  }
}

function copyResult() {
  // 使用保存的原始文本而不是从DOM获取，确保复制的是纯文本格式
  const text = savedResult || document.getElementById('result').innerText;
  navigator.clipboard.writeText(text).then(() => {
    alert('已复制到剪贴板');
  }).catch(err => {
    console.error('复制失败:', err);
    alert('复制失败，请手动复制');
  });
}

function saveSettings() {
  const apiPlatform = document.getElementById('apiPlatform').value;
  const baseUrl = document.getElementById('baseUrl').value;
  const model = document.getElementById('modelSelect').value;
  const customModel = document.getElementById('customModel').value;
  const language = document.getElementById('languageSelect').value;
  const fontSize = document.getElementById('fontSizeSelect').value;
  const statusDisplay = showStatusDisplay ? 'on' : 'off';

  if (apiPlatform !== 'ollama') {
    const apiKey = document.getElementById('apiKey').value.trim();
    apiKeys[apiPlatform] = apiKey;
  }

  console.log('Saving settings:', {apiPlatform, baseUrl, model, customModel, language, fontSize, statusDisplay});

  chrome.storage.sync.set({apiKeys, apiPlatform, baseUrl, model, customModel, language, fontSize, statusDisplay}, function() {
    if (chrome.runtime.lastError) {
      console.error('Error saving settings:', chrome.runtime.lastError);
    } else {
      currentPlatform = apiPlatform;
      currentLanguage = language;
      showStatusDisplay = (statusDisplay === 'on');
      updateLanguage();
      updateFontSize(fontSize);
      console.log('Settings saved successfully');
    }
  });
}

function loadSettings() {
  chrome.storage.sync.get(['apiKeys', 'apiPlatform', 'model', 'customModel', 'currentTheme', 'language', 'fontSize', 'statusDisplay'], function(result) {
    apiKeys = result.apiKeys || {};
    currentPlatform = result.apiPlatform || Object.keys(apiConfigs)[0];
    document.getElementById('apiPlatform').value = currentPlatform;
    
    // SiliconFlow密钥现在统一通过apiKeys管理，无需特殊处理
    
    if (currentPlatform !== 'ollama') {
      document.getElementById('apiKey').value = apiKeys[currentPlatform] || '';
      document.getElementById('apiKey').style.display = 'block';
    } else {
      document.getElementById('apiKey').style.display = 'none';
    }
    // API密钥加载日志已移除，避免控制台泄露
    console.log('Current Platform:', currentPlatform);
    // 具体密钥信息已移除，避免控制台泄露

    updatePlatformOptions();
    document.getElementById('modelSelect').value = result.model || apiConfigs[currentPlatform].models[0].id;
    document.getElementById('customModel').value = result.customModel || '';
    toggleCustomModelInput();
    updateBaseUrl();
    
    if (result.currentTheme !== undefined) {
      currentThemeIndex = result.currentTheme;
      switchTheme();
    }
    
    if (result.language) {
      currentLanguage = result.language;
      document.getElementById('languageSelect').value = currentLanguage;
      updateLanguage();
    }
    
    if (result.fontSize) {
      document.getElementById('fontSizeSelect').value = result.fontSize;
      updateFontSize(result.fontSize);
    }

    // 加载状态显示设置
    if (result.statusDisplay !== undefined) {
      showStatusDisplay = (result.statusDisplay === 'on');
      document.getElementById('statusDisplayToggle').value = result.statusDisplay;
    } else {
      // 默认开启
      showStatusDisplay = true;
      document.getElementById('statusDisplayToggle').value = 'on';
    }
    
    console.log('Loaded settings:', result);

    // 在这里调用新函数
    setupApiKeyToggle();
  });
}

function toggleCustomModelInput() {
  const modelSelect = document.getElementById('modelSelect');
  const customModelInput = document.getElementById('customModel');
  customModelInput.style.display = modelSelect.value === 'custom' ? 'block' : 'none';
}

function clearAll() {
  console.log('clearAll function called');
  clearResult();
  document.getElementById('customPrompt').value = '';
  chrome.storage.local.remove(['lastResult']);
  // 不要清除 currentContent
  conversationHistory = [];
  savedResult = '';  // 清除保存的结果
  // 不要清除 currentUrl

  // 清除当前页面的缓存
  Object.keys(responseCache).forEach(key => {
    if (key.includes(currentUrl)) {
      delete responseCache[key];
    }
  });

  // 隐藏状态栏
  hideStatus();

  // 重置笔记计数器
  noteCounter = 0;
  chrome.storage.local.set({noteCounter: 0});
}

function toggleSettings() {
  const settingsPanel = document.getElementById('settingsPanel');
  const overlay = document.getElementById('overlay');
  const elementsToHide = document.querySelectorAll('#summarizeBtn, #askBtn, #clearBtn, #exportBtn, #customPrompt, #result, #themeSwitcher');
  
  if (settingsPanel.style.display === 'none') {
    settingsPanel.style.display = 'block';
    if (overlay) {
      overlay.style.display = 'block';
    }
    elementsToHide.forEach(el => el.style.visibility = 'hidden');
  } else {
    settingsPanel.style.display = 'none';
    if (overlay) {
      overlay.style.display = 'none';
    }
    elementsToHide.forEach(el => el.style.visibility = 'visible');
  }
}

function clearResult() {
  document.getElementById('result').innerText = '';
  conversationHistory = [];
  document.getElementById('copyBtn').style.display = 'none'; // 隐藏复制按钮
  savedResult = '';  // 清除保存的结果
  // 不删除storage中的结果，让它保持到下次新问题时被覆盖
}

function loadLastResult() {
  chrome.storage.local.get(['lastResult'], function(result) {
    if (result.lastResult) {
      typeWriter(result.lastResult);
    }
  });
}

function truncateContent(content, maxLength) {
  if (content.length <= maxLength) return content;
  return content.substr(0, maxLength) + '...（内容已截断）';
}

function askCustomQuestion() {
  incrementClickCount(); // 增加点击计数

  const customPrompt = document.getElementById('customPrompt').value;
  if (customPrompt) {
    if (currentContent) {
      callModelAPI(`${getLanguagePrompt()}\n\n${languages[currentLanguage].baseContent}:\n\n${truncateContent(currentContent, 40000)}\n\n${languages[currentLanguage].question}: ${customPrompt}\n\n${getLanguagePrompt()}`, false, true, `${languages[currentLanguage].question}: ${customPrompt}`);
    } else {
      // 如果 currentContent 为空，尝试重新获取页面内容
      chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
        if (tabs[0]) {
          getPageContent(tabs[0].id);
          setTimeout(() => {
            if (currentContent) {
              callModelAPI(`${getLanguagePrompt()}\n\n${languages[currentLanguage].baseContent}:\n\n${truncateContent(currentContent, 40000)}\n\n${languages[currentLanguage].question}: ${customPrompt}\n\n${getLanguagePrompt()}`, false, true, `${languages[currentLanguage].question}: ${customPrompt}`);
            } else {
              document.getElementById('result').innerText = languages[currentLanguage].noContent;
            }
          }, 1000); // 给予一些时间来获取内容
        } else {
          document.getElementById('result').innerText = languages[currentLanguage].noTabInfo;
        }
      });
    }
  } else {
    document.getElementById('result').innerText = languages[currentLanguage].enterQuestion;
  }
}

// 优化性能的函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 在文件末尾添加
window.onbeforeunload = function() {
  // 保存笔记计数器
  chrome.storage.local.set({noteCounter: noteCounter});
};

// 新增: 保存到 chrome.storage.local 的函数已经在 displayResult 中实现

function incrementClickCount() {
  chrome.storage.sync.get(['clickCount'], function(result) {
    let newCount = (result.clickCount || 0) + 1;
    chrome.storage.sync.set({clickCount: newCount}, function() {
      updateClickCountDisplay(newCount);
    });
  });
}

function updateClickCountDisplay(count) {
  const counterElement = document.getElementById('clickCounter');
  counterElement.textContent = count;
}

function loadClickCount() {
  chrome.storage.sync.get(['clickCount'], function(result) {
    let count = result.clickCount || 0;
    updateClickCountDisplay(count);
  });
}

const themes = [
  {
    name: 'yellow',
    primary: '#fff9c4',
    secondary: '#ffeb3b',
    tertiary: '#fdd835',
    text: '#333'
  },
  {
    name: 'pink',
    primary: '#fce4ec',
    secondary: '#f8bbd0',
    tertiary: '#f48fb1',
    text: '#333'
  },
  {
    name: 'green',
    primary: '#f1f8e9',
    secondary: '#dcedc8',
    tertiary: '#c5e1a5',
    text: '#333'
  }
];

let currentThemeIndex = 0;

function switchTheme() {
  currentThemeIndex = (currentThemeIndex + 1) % themes.length;
  const theme = themes[currentThemeIndex];
  document.documentElement.style.setProperty('--primary-color', theme.primary);
  document.documentElement.style.setProperty('--secondary-color', theme.secondary);
  document.documentElement.style.setProperty('--tertiary-color', theme.tertiary);
  document.documentElement.style.setProperty('--text-color', theme.text);
  
  // 更新主题切换按钮的颜色
  document.getElementById('themeSwitcher').style.backgroundColor = theme.secondary;
  
  // 保存当前主题
  chrome.storage.sync.set({currentTheme: currentThemeIndex});
}

function updateLanguage() {
  const lang = languages[currentLanguage];
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    if (lang[key]) {
      element.textContent = lang[key];
    }
  });
  
  // 更新动态创建的元素或不使用 data-i18n 属性的元素
  const h1 = document.querySelector('h1');
  if (h1) {
    h1.textContent = lang.title;
  }
  const copyBtn = document.getElementById('copyBtn');
  if (copyBtn) {
    copyBtn.innerHTML = `${lang.copy}`; // 移除图标
  }
  const clearBtn = document.getElementById('clearBtn');
  if (clearBtn) {
    clearBtn.innerHTML = `${lang.clear}`; // 移除图标
  }
  const fontSizeLabel = document.getElementById('fontSizeLabel');
  if (fontSizeLabel) {
    fontSizeLabel.textContent = lang.fontSize;
  }
  const saveSettingsBtn = document.getElementById('saveSettings');
  if (saveSettingsBtn) {
    saveSettingsBtn.innerHTML = `${lang.saveSettings}`; // 移除图标
  }
  const exportBtn = document.getElementById('exportBtn');
  if (exportBtn) {
    exportBtn.innerHTML = `${lang.export}`; // 移除图标
  }
  const clearNotesBtn = document.getElementById('clearNotesBtn');
  if (clearNotesBtn) {
    clearNotesBtn.innerHTML = `${lang.clearNotes}`;
  }
  const statusDisplayLabel = document.getElementById('statusDisplayLabel');
  if (statusDisplayLabel) {
    statusDisplayLabel.textContent = lang.statusDisplay;
  }
  const settingsBtn = document.getElementById('settingsBtn');
  if (settingsBtn) {
    settingsBtn.innerHTML = `⚙️`; // 保持齿轮图标，不使用文字
  }
  const askBtn = document.getElementById('askBtn');
  if (askBtn) {
    askBtn.innerHTML = `${lang.ask}`; // 移除图标
  }
  const summarizeBtn = document.getElementById('summarizeBtn');
  if (summarizeBtn) {
    summarizeBtn.innerHTML = `${lang.summarize}`; // 移除图标
  }

  // 最后强制确保设置按钮保持齿轮图标
  const settingsBtnFinal = document.getElementById('settingsBtn');
  if (settingsBtnFinal) {
    settingsBtnFinal.innerHTML = '⚙️';
  }
}

function updateFontSize(size) {
  const resultElement = document.getElementById('result');
  switch(size) {
    case 'small':
      resultElement.style.fontSize = '12px';
      break;
    case 'medium':
      resultElement.style.fontSize = '16px';
      break;
    case 'large':
      resultElement.style.fontSize = '20px';
      break;
  }
}

function setupApiKeyToggle() {
  const toggleBtn = document.getElementById('toggleApiKeyVisibility');
  const apiKeyInput = document.getElementById('apiKey');
  
  if (toggleBtn && apiKeyInput) {
    toggleBtn.addEventListener('click', function() {
      console.log('Toggle button clicked');
      if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        this.textContent = '👁️‍🗨️';
        this.style.fontSize = '16px';
      } else {
        apiKeyInput.type = 'password';
        this.textContent = '👁️';
        this.style.fontSize = '14px';
      }
    });
    console.log('API Key toggle setup complete');
  } else {
    console.error('Toggle button or API Key input not found');
  }
}

// 新增: 导出按钮事件监听器已经在 DOMContentLoaded 中添加

// 清理不完整的结尾
function cleanIncompleteEnding(text) {
  // 移除常见的不完整结尾字符
  const incompletePatterns = [
    /（$/, // 单独的左括号
    /，$/, // 结尾的逗号
    /、$/, // 结尾的顿号
    /：$/, // 结尾的冒号
    /；$/, // 结尾的分号
    /\($/, // 英文左括号
    /,$/, // 英文逗号
    /\.\.\.$/, // 省略号
    /\s+$/ // 结尾空格
  ];

  let cleanedText = text;
  incompletePatterns.forEach(pattern => {
    cleanedText = cleanedText.replace(pattern, '');
  });

  // 确保以句号结尾
  if (cleanedText && !cleanedText.match(/[。！？]$/)) {
    cleanedText += '。';
  }

  console.log('清理前长度:', text.length, '清理后长度:', cleanedText.length);
  return cleanedText;
}

// 显示状态信息
function showStatus(info) {
  // 只有在开启状态显示时才显示
  if (!showStatusDisplay) {
    return;
  }

  const statusBar = document.getElementById('statusBar');
  const statusInfo = document.getElementById('statusInfo');
  if (statusBar && statusInfo) {
    statusInfo.innerHTML = info;
    statusBar.style.display = 'block';
  }
}

// 隐藏状态信息
function hideStatus() {
  const statusBar = document.getElementById('statusBar');
  if (statusBar) {
    statusBar.style.display = 'none';
  }
}

// 切换状态栏显示
function toggleStatusDisplay() {
  const toggleSelect = document.getElementById('statusDisplayToggle');
  const statusBar = document.getElementById('statusBar');

  // 根据下拉框的值更新状态
  showStatusDisplay = (toggleSelect.value === 'on');

  if (!showStatusDisplay) {
    // 关闭状态 - 立即隐藏状态栏
    if (statusBar) {
      statusBar.style.display = 'none';
    }
  }

  // 保存设置
  chrome.storage.sync.set({statusDisplay: toggleSelect.value});

  console.log('状态栏显示切换为:', showStatusDisplay, '值:', toggleSelect.value);
}

