// 侧边栏功能 - 点击扩展图标时打开侧边栏
chrome.action.onClicked.addListener(async (tab) => {
  console.log('🌙 画梦录扩展图标被点击，准备打开侧边栏');
  
  // 检查Chrome版本和API支持
  const chromeVersion = navigator.userAgent.match(/Chrome\/(\d+)/);
  const version = chromeVersion ? parseInt(chromeVersion[1]) : 0;
  
  console.log(`🔍 检测到Chrome版本: ${version}`);
  
  if (version < 114) {
    console.log('❌ Chrome版本过低，不支持侧边栏功能');
    return;
  }
  
  if (!chrome.sidePanel) {
    console.log('❌ sidePanel API不可用');
    return;
  }
  
  try {
    console.log('📂 尝试打开侧边栏...');
    
    // 打开侧边栏
    await chrome.sidePanel.open({
      tabId: tab.id
    });
    
    console.log('✅ 画梦录侧边栏已成功打开');
  } catch (error) {
    console.log('❌ 打开侧边栏失败:', error);
    
    // 如果侧边栏打开失败，尝试打开独立窗口作为备选方案
    try {
      console.log('🔄 尝试打开独立窗口作为备选方案...');
      
      await chrome.windows.create({
        url: chrome.runtime.getURL('popup.html'),
        type: 'popup',
        width: 400,
        height: 700,
        focused: true
      });
      
      console.log('✅ 独立窗口已打开');
    } catch (windowError) {
      console.log('❌ 打开独立窗口也失败:', windowError);
    }
  }
});

// 调试信息
console.log('画梦录背景脚本初始化完成 - 侧边栏功能已启用');
