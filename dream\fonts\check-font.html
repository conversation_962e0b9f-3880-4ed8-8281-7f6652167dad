<!DOCTYPE html>
<html>
<head>
  <title>画梦录 - 字体检测工具</title>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #3b82f6;
      text-align: center;
    }
    
    .font-test {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .font-name {
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .sample {
      font-size: 24px;
      line-height: 1.5;
    }
    
    .status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
    }
    
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    
    .warning {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    @font-face {
      font-family: 'LocalHandwritten';
      src: url('NiHeWoDeLangManYuZhou-2.ttf') format('truetype');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
    
    .handwritten {
      font-family: 'LocalHandwritten', cursive, sans-serif;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>画梦录 - 字体检测工具</h1>
    
    <div class="font-test">
      <div class="font-name">本地手写字体测试</div>
      <div class="sample handwritten">梦境记录：我梦见自己在一片广阔的森林中漫步，阳光透过树叶洒落在地面上，形成斑驳的光影。</div>
      <div id="localFontStatus" class="status">检测中...</div>
    </div>
    
    <div class="font-test">
      <div class="font-name">系统默认字体测试</div>
      <div class="sample">梦境记录：我梦见自己在一片广阔的森林中漫步，阳光透过树叶洒落在地面上，形成斑驳的光影。</div>
    </div>
    
    <div id="instructions"></div>
  </div>
  
  <script>
    window.addEventListener('load', function() {
      setTimeout(function() {
        const handwrittenSample = document.querySelector('.handwritten');
        const localFontStatus = document.getElementById('localFontStatus');
        const instructions = document.getElementById('instructions');
        
        // 创建字体检测元素
        const fontDetector = document.createElement('span');
        fontDetector.style.fontFamily = 'LocalHandwritten';
        fontDetector.style.visibility = 'hidden';
        fontDetector.style.position = 'absolute';
        fontDetector.textContent = 'test';
        document.body.appendChild(fontDetector);
        
        // 获取计算后的字体
        const computedStyle = window.getComputedStyle(fontDetector);
        const loadedFont = computedStyle.fontFamily;
        
        // 检测字体是否加载成功
        if (loadedFont.indexOf('LocalHandwritten') !== -1) {
          localFontStatus.textContent = '✓ 本地手写字体加载成功！';
          localFontStatus.className = 'status success';
          
          instructions.innerHTML = `
            <div class="status success">
              <h3>恭喜！字体已正确安装</h3>
              <p>您现在可以在"画梦录"插件中使用手写字体功能了。请确保在设置中选择"手写风格"选项。</p>
            </div>
          `;
        } else {
          localFontStatus.textContent = '✗ 本地手写字体未能加载，可能未正确安装';
          localFontStatus.className = 'status error';
          
          instructions.innerHTML = `
            <div class="status warning">
              <h3>字体未正确加载</h3>
              <p>请按照以下步骤安装字体：</p>
              <ol>
                <li>确认您已下载手写字体文件（.ttf或.otf格式）</li>
                <li>将字体文件复制到<code>dream/fonts/</code>目录</li>
                <li>确保字体文件名为<code>NiHeWoDeLangManYuZhou-2.ttf</code>或修改CSS中的引用</li>
                <li>刷新此页面检查字体是否加载成功</li>
              </ol>
              <p>如果仍然无法加载，请尝试重启浏览器或清除浏览器缓存。</p>
            </div>
          `;
        }
        
        // 清理检测元素
        document.body.removeChild(fontDetector);
      }, 1000);
    });
  </script>
</body>
</html> 