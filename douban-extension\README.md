# 豆瓣评论分析器 - 浏览器扩展版

这是一个Chrome浏览器扩展，可以帮助你分析豆瓣上的电影和图书评论，并通过AI生成评论摘要报告。

## 主要功能

1. **自动检测当前页面**：在豆瓣电影或图书页面自动获取ID和类型
2. **评论采集**：自动收集电影或图书的用户评论
3. **AI分析**：使用AI技术分析评论并生成摘要报告
4. **快速洞察**：帮助你快速了解其他用户对电影或图书的整体评价

## 安装方法

1. 下载并解压此扩展
2. 打开Chrome浏览器，转到 `chrome://extensions/`
3. 启用右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择解压后的扩展文件夹

## 使用方法

### 基本使用

1. 浏览到任意豆瓣电影或图书页面
2. 点击工具栏中的豆瓣评论分析器图标
3. 输入您的豆瓣账号和密码（用于获取评论数据）
4. 点击"开始分析"按钮

### 设置API密钥

1. 点击扩展界面右下角的齿轮图标
2. 在设置面板中输入您的OpenAI API密钥
3. 点击"保存"按钮

## 隐私说明

- 您的豆瓣账号信息仅用于获取评论数据，不会被发送到除豆瓣以外的任何服务器
- API密钥仅存储在您的浏览器本地，用于调用AI服务
- 所有数据处理均在本地完成，评论内容仅用于AI分析

## 注意事项

- 请遵守豆瓣的使用条款和服务协议
- 请合理使用API配额，避免频繁请求
- 分析结果仅供参考，可能不完全准确

## 技术支持

如有问题或建议，请联系开发者或提交Issue。

---

© 2023 豆瓣评论分析器 | 版本 1.0 