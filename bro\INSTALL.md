# 📥 AI便利贴安装指南

## 系统要求

### 浏览器要求
- **Chrome**: 版本 114 或更高
- **Edge**: 版本 114 或更高（基于Chromium）
- **Opera**: 版本 100 或更高（基于Chromium）
- **Brave**: 最新版本

### 操作系统
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 18.04+)

## 安装步骤

### 方法一：开发者模式安装（推荐）

#### 1. 下载源码
```bash
# 使用Git克隆
git clone https://github.com/your-username/bro.git

# 或直接下载ZIP文件并解压
```

#### 2. 启用开发者模式
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 点击右上角的"开发者模式"开关
4. 确保开关处于开启状态

#### 3. 加载扩展
1. 点击"加载已解压的扩展程序"按钮
2. 选择下载的项目文件夹（包含manifest.json的文件夹）
3. 点击"选择文件夹"
4. 扩展安装完成

#### 4. 验证安装
- 在浏览器工具栏看到AI便利贴图标（智）
- 点击图标能正常打开侧边栏
- 设置页面功能正常

### 方法二：打包安装

#### 1. 打包扩展
1. 在扩展管理页面点击"打包扩展程序"
2. 选择项目根目录
3. 生成 `.crx` 和 `.pem` 文件

#### 2. 安装打包文件
1. 将 `.crx` 文件拖拽到扩展管理页面
2. 点击"添加扩展程序"确认安装

## 首次配置

### 1. 获取API密钥

#### OpenAI
1. 访问 https://platform.openai.com/api-keys
2. 登录或注册账户
3. 点击"Create new secret key"
4. 复制生成的API密钥

#### SiliconFlow
1. 访问 https://siliconflow.cn/
2. 注册账户（通常有免费额度）
3. 在控制台获取API密钥

#### Google Gemini
1. 访问 https://aistudio.google.com/
2. 创建项目
3. 在API Keys页面生成密钥

### 2. 配置扩展
1. 点击扩展图标
2. 点击左上角齿轮图标⚙️
3. 选择AI平台
4. 输入对应的API密钥
5. 点击"保存设置"

### 3. 测试功能
1. 打开任意网页
2. 点击"I'm OK"按钮测试总结功能
3. 在文本框输入问题测试问答功能

## 故障排除

### 安装问题

#### 扩展无法加载
**错误信息**: "无法加载扩展"
**解决方案**:
1. 检查文件夹是否包含 `manifest.json`
2. 确认Chrome版本是否支持
3. 检查文件权限是否正确

#### 权限错误
**错误信息**: "权限被拒绝"
**解决方案**:
1. 确保开发者模式已开启
2. 重新加载扩展
3. 重启浏览器

### 功能问题

#### API调用失败
**症状**: 显示"请在设置中输入API Key"
**解决方案**:
1. 检查API密钥格式是否正确
2. 确认账户余额充足
3. 验证网络连接

#### 侧边栏无法打开
**症状**: 点击图标无反应
**解决方案**:
1. 检查Chrome版本（需要114+）
2. 重新加载扩展
3. 清除浏览器缓存

## 更新扩展

### 开发者模式更新
1. 下载最新版本源码
2. 在扩展管理页面点击"重新加载"按钮
3. 或删除旧版本，重新安装新版本

### 保留设置
- 扩展设置会自动保存
- 笔记数据会保留
- API密钥需要重新输入（安全考虑）

## 卸载扩展

### 完全卸载
1. 在扩展管理页面找到AI便利贴
2. 点击"移除"按钮
3. 确认删除

### 清理数据
```javascript
// 在控制台执行以清理存储数据
chrome.storage.local.clear();
chrome.storage.sync.clear();
```

## 技术支持

如果遇到安装问题，请：
1. 查看浏览器控制台错误信息
2. 在GitHub Issues提交问题
3. 提供详细的错误描述和系统信息

---

**安装成功后，您就可以开始使用AI便利贴的强大功能了！** 🎉
