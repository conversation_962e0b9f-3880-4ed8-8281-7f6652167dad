# 日期天气卡片浏览器插件

这是一个集成了日期显示和天气预报功能的浏览器扩展程序。

## 功能特点

- 优雅美观的卡片设计，色彩搭配清新自然
- 显示当天日期、农历日期和节假日信息
- 实时天气信息展示，包括温度、天气状态和图标
- 提供未来三天天气预报功能
- 基于地理位置的天气数据，准确实用
- **月份花卉艺术系统**：按12个月份精准分配代表性花卉，贴合自然节令
- **AI花卉图片生成**：集成Gemini AI，生成符合当月特色的花卉艺术图片
- **多样艺术风格**：支持国画、工笔画、水彩、油画等多种传统艺术风格
- 日期切换功能（前一天、今天、后一天）
- 键盘左右箭头按键支持
- 平滑的过渡动画效果
- 响应式设计，适配不同屏幕尺寸

## 安装方法

### 获取API密钥（免费）

此插件使用OpenWeatherMap API获取天气数据。在使用前，您需要：

1. 访问 [OpenWeatherMap官网](https://openweathermap.org/) 并注册一个账号
2. 注册成功后，登录并前往 [API Keys](https://home.openweathermap.org/api_keys) 页面
3. 创建一个新的API密钥（可以使用任意名称）
4. 复制生成的API密钥字符串

注册完成后，您需要将API密钥添加到扩展中：

1. 打开 `js/script.js` 文件
2. 找到 `const weatherApiKey = 'YOUR_API_KEY';` 这一行
3. 将 `YOUR_API_KEY` 替换为您实际获取的API密钥
4. 保存文件

### 安装插件

#### Chrome浏览器：

1. 打开Chrome浏览器，进入 chrome://extensions/
2. 打开右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择整个 date-card 文件夹
5. 安装完成后，插件图标会出现在浏览器工具栏

#### Edge浏览器：

1. 打开Edge浏览器，进入 edge://extensions/
2. 打开左下角的"开发人员模式"
3. 点击"加载解压缩的扩展"
4. 选择整个 date-card 文件夹
5. 安装完成后，插件图标会出现在浏览器工具栏

#### Firefox浏览器：

1. 打开Firefox浏览器，进入 about:debugging#/runtime/this-firefox
2. 点击"临时加载附加组件"
3. 选择 date-card 文件夹中的 manifest.json 文件
4. 安装完成后，插件会暂时加载（Firefox关闭后需要重新加载）

## 项目结构

```
date-card/
├── manifest.json    # 扩展清单文件
├── index.html       # 主页面
├── icons/           # 图标文件夹
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
├── css/
│   └── style.css    # 样式和动画效果
└── js/
    ├── script.js    # 交互和日期天气逻辑
    └── generate-icons.js   # 图标生成工具
```

## 关于天气API

本扩展使用OpenWeatherMap API获取天气数据，具有以下特点：

- **完全免费**：免费计划足够个人使用
- **全球覆盖**：支持全球大多数城市的天气数据
- **实时数据**：提供当前天气和5天预报功能
- **使用限制**：免费账户每天可请求1,000次API调用（约每90秒一次）
- **自动定位**：自动获取用户地理位置（需要授予权限）
- **中文支持**：支持返回中文天气描述
- **丰富数据**：提供温度、天气状态、图标等多种数据

### 其他免费天气API选项

如果您想尝试其他免费的天气API，以下是几个不错的选择：

1. **和风天气**：中国服务提供商，对国内城市支持更好
   - 网址：[https://dev.qweather.com/](https://dev.qweather.com/)
   - 免费计划：每天1,000次API调用
   - 优点：对中国城市支持更好，中文数据更准确

2. **AccuWeather**：全球天气服务提供商
   - 网址：[https://developer.accuweather.com/](https://developer.accuweather.com/)
   - 免费计划：每天50次API调用
   - 优点：数据准确性高

3. **Weather API (weatherapi.com)**：易于使用的全球天气API
   - 网址：[https://www.weatherapi.com/](https://www.weatherapi.com/)
   - 免费计划：每月1,000,000次API调用
   - 优点：调用限制高，接口简洁

### 替换为其他API

如果您想替换为其他天气API，需要修改 `script.js` 文件中的以下几个部分：

1. API配置部分（更改API URL和认证方式）
2. 数据处理函数（调整解析返回数据的方式）
3. 天气图标映射（根据新API的图标代码调整）

## 🌸 月份花卉分布

插件采用精细化的月份花卉系统，每个月都有其特色的代表性花卉，更贴合自然节令和传统文化：

### 春季花卉（1-5月）

**🌨️ 一月 - 寒冬腊月**
- 梅花（国画、水墨、写意）
- 水仙（工笔画、国画、水彩）
- 山茶花（油画、工笔画、国画）

**🌱 二月 - 立春时节**
- 梅花（国画、水墨、写意）
- 迎春花（水彩、国画、油画）
- 山茶花（油画、工笔画、国画）

**🌸 三月 - 春暖花开**
- 桃花（水彩、国画、浮世绘）
- 樱花（浮世绘、水墨、水彩）
- 玉兰（油画、国画、工笔画）
- 迎春花（水彩、国画、油画）

**🌺 四月 - 暮春时节**
- 牡丹（国画、工笔画、油画）
- 海棠（国画、水彩、工笔画）
- 杏花（水墨、国画、水彩）
- 梨花（水彩、国画、素描）

**🌹 五月 - 晚春初夏**
- 牡丹（国画、工笔画、油画）
- 芍药（工笔画、国画、水彩）
- 月季（油画、水彩、版画）
- 蔷薇（水彩、油画、国画）

### 夏季花卉（6-8月）

**🌼 六月 - 初夏时节**
- 栀子花（水彩、国画、油画）
- 茉莉花（工笔画、水彩、国画）
- 荷花（国画、工笔画、水墨）
- 月季（油画、水彩、版画）

**☀️ 七月 - 盛夏时光**
- 荷花（国画、工笔画、水墨）
- 向日葵（油画、版画、水彩）
- 茉莉花（工笔画、水彩、国画）
- 紫薇（国画、水彩、工笔画）

**🌻 八月 - 夏末秋初**
- 荷花（国画、工笔画、水墨）
- 向日葵（油画、版画、水彩）
- 紫薇（国画、水彩、工笔画）
- 木槿（水彩、国画、油画）

### 秋季花卉（9-11月）

**🍂 九月 - 金秋时节**
- 桂花（工笔画、国画、水彩）
- 菊花（国画、工笔画、水墨）
- 木芙蓉（国画、水彩、工笔画）

**🍁 十月 - 深秋时分**
- 菊花（国画、工笔画、水墨）
- 桂花（工笔画、国画、水彩）
- 木芙蓉（国画、水彩、工笔画）
- 海棠果（水彩、国画、油画）

**🍃 十一月 - 晚秋时节**
- 菊花（国画、工笔画、水墨）
- 山茶花（油画、工笔画、国画）
- 木芙蓉（国画、水彩、工笔画）

### 冬季花卉（12月）

**❄️ 十二月 - 初冬时光**
- 梅花（国画、水墨、写意）
- 山茶花（油画、工笔画、国画）
- 水仙（工笔画、国画、水彩）

### 花卉选择机制

- **月份智能匹配**：根据当前日期自动选择对应月份的花卉池
- **真正随机选择**：在当月的多种花卉中完全随机选择，每次刷新都可能不同
- **双重随机性**：花卉种类随机 + 艺术风格随机，确保丰富多样的体验
- **AI智能生成**：使用Gemini AI根据随机选中的花卉名称和风格生成艺术图片
- **优雅降级**：AI生成失败时使用精美的占位图片作为备用方案

### 随机体验示例
- **7月份**：可能显示荷花、向日葵、茉莉花或紫薇中的任意一种
- **每次刷新**：即使是同一天，也可能看到当月不同的花卉
- **风格变化**：同一种花卉也会以不同艺术风格呈现（国画、工笔画、水彩等）

## 自定义

你可以通过修改以下文件来自定义卡片：

- `css/style.css` - 修改颜色、布局、动画效果和尺寸
- `js/script.js` - 修改节假日数据或添加新功能
- `manifest.json` - 修改扩展信息和权限

## 隐私说明

此扩展需要以下权限：

- **存储**：保存用户的API密钥和设置偏好
- **网络请求**：访问以下API服务
  - OpenMeteo API：获取免费天气数据
  - Gemini AI API：生成花卉艺术图片

### 数据使用说明

- **天气数据**：使用OpenMeteo免费API，无需个人信息
- **AI图片生成**：仅发送花卉名称和艺术风格到Gemini API
- **本地存储**：API密钥和用户设置仅保存在本地浏览器中
- **隐私保护**：不收集、存储或传输任何个人隐私数据

## 常见问题

### 天气相关

1. **天气数据无法显示怎么办？**
   - 插件使用OpenMeteo免费API，无需注册
   - 检查网络连接是否正常
   - 查看浏览器控制台是否有API请求错误

2. **如何更改默认显示的城市位置？**
   - 插件支持北京市各区的天气查询
   - 可以在设置中选择不同的区域
   - 默认显示北京市天气

### 花卉艺术相关

3. **为什么花卉图片无法生成？**
   - 需要配置Gemini API密钥才能使用AI生成功能
   - 可以在设置面板中输入API密钥
   - 未配置API时会显示精美的占位图片

4. **如何获取Gemini API密钥？**
   - 访问 [Google AI Studio](https://aistudio.google.com/)
   - 注册并创建API密钥
   - 在插件设置中输入密钥即可使用

5. **花卉为什么不符合当前季节？**
   - 插件按照12个月份精准分配花卉
   - 每个月都有其特色的代表性花卉
   - 花卉选择完全随机，如果觉得不合适，可以刷新重新生成

6. **为什么每次刷新显示的花卉都不一样？**
   - 这是设计特色！插件在当月花卉中完全随机选择
   - 同一天可能显示当月任意一种花卉，增加惊喜感
   - 艺术风格也是随机的，确保每次体验都独特

### 技术问题

6. **扩展无法加载或显示异常怎么办？**
   - 确保所有文件都在正确的位置
   - 在浏览器扩展页面重新加载扩展
   - 查看浏览器控制台是否有错误信息

7. **API请求频率限制怎么办？**
   - Gemini API有请求频率限制
   - 插件内置10秒冷却机制
   - 建议间隔使用以避免超出限制

## 天气图标说明

- <i class="fas fa-sun"></i> - 晴天
- <i class="fas fa-cloud"></i> - 多云
- <i class="fas fa-cloud-sun"></i> - 多云转晴
- <i class="fas fa-cloud-rain"></i> - 小雨
- <i class="fas fa-cloud-showers-heavy"></i> - 大雨
- <i class="fas fa-snowflake"></i> - 雪

## 注意事项

### 网络依赖
- 本项目使用了Font Awesome和Weather Icons图标库，需要保持网络连接
- 天气数据来自OpenMeteo免费API，需要网络连接
- AI图片生成需要Gemini API，需要网络连接和有效的API密钥

### 数据说明
- 天气数据为实时数据，来自OpenMeteo API
- 农历信息是简化版本，仅用于演示
- 花卉分布基于传统节令，可能因地区而异

### 使用建议
- 建议配置Gemini API密钥以获得最佳体验
- API请求有频率限制，建议适度使用
- 在生产环境中，建议使用付费API以获得更稳定的服务

### 开发说明
- 项目采用原生JavaScript开发，无需额外框架
- 支持Chrome、Edge、Firefox等主流浏览器
- 代码结构清晰，易于二次开发和定制