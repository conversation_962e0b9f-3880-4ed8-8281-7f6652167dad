// 全局变量定义
// 当前显示的日期
let currentDate = new Date();

// 天气API配置
const weatherApiUrl = 'https://api.open-meteo.com/v1';

// 默认城市及地区配置
const locations = {
    'beijing': { lat: 39.9042, lon: 116.4074, name: '北京市' },
    'chaoyang': { lat: 39.9219, lon: 116.4431, name: '朝阳区' },
    'haidian': { lat: 39.9634, lon: 116.3160, name: '海淀区' },
    'dongcheng': { lat: 39.9289, lon: 116.4160, name: '东城区' },
    'xicheng': { lat: 39.9126, lon: 116.3666, name: '西城区' },
    'fengtai': { lat: 39.8580, lon: 116.2870, name: '丰台区' },
    'shijingshan': { lat: 39.9060, lon: 116.2230, name: '石景山区' },
    'changping': { lat: 40.2210, lon: 116.2140, name: '昌平区' },
    'daxing': { lat: 39.7270, lon: 116.3410, name: '大兴区' },
    'tongzhou': { lat: 39.9154, lon: 116.6560, name: '通州区' },
    'shunyi': { lat: 40.1302, lon: 116.6590, name: '顺义区' }
};

// 默认城市
let userCity = 'beijing';
let userLocation = locations['beijing'];

// 农历数据（简化版）
const lunarInfo = {
    monthNames: ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'],
    dayNames: ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十', 
               '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十', 
               '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十']
};

// 节假日数据
const holidays = {
    '1-1': '元旦',
    '2-14': '情人节',
    '3-8': '妇女节',
    '4-1': '愚人节',
    '5-1': '劳动节',
    '6-1': '儿童节',
    '10-1': '国庆节',
    '12-25': '圣诞节'
};

// 中文月份名称
const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];

// 星期几名称
const weekdayNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

// 按月份分配的花卉数据 - 更贴合节令
const flowers = {
    1: [ // 一月 - 寒冬腊月
        { name: '梅花', styles: ['国画', '水墨', '写意'] },
        { name: '水仙', styles: ['工笔画', '国画', '水彩'] },
        { name: '山茶花', styles: ['油画', '工笔画', '国画'] }
    ],
    2: [ // 二月 - 立春时节
        { name: '梅花', styles: ['国画', '水墨', '写意'] },
        { name: '迎春花', styles: ['水彩', '国画', '油画'] },
        { name: '山茶花', styles: ['油画', '工笔画', '国画'] }
    ],
    3: [ // 三月 - 春暖花开
        { name: '桃花', styles: ['水彩', '国画', '浮世绘'] },
        { name: '樱花', styles: ['浮世绘', '水墨', '水彩'] },
        { name: '玉兰', styles: ['油画', '国画', '工笔画'] },
        { name: '迎春花', styles: ['水彩', '国画', '油画'] }
    ],
    4: [ // 四月 - 暮春时节
        { name: '牡丹', styles: ['国画', '工笔画', '油画'] },
        { name: '海棠', styles: ['国画', '水彩', '工笔画'] },
        { name: '杏花', styles: ['水墨', '国画', '水彩'] },
        { name: '梨花', styles: ['水彩', '国画', '素描'] }
    ],
    5: [ // 五月 - 晚春初夏
        { name: '牡丹', styles: ['国画', '工笔画', '油画'] },
        { name: '芍药', styles: ['工笔画', '国画', '水彩'] },
        { name: '月季', styles: ['油画', '水彩', '版画'] },
        { name: '蔷薇', styles: ['水彩', '油画', '国画'] }
    ],
    6: [ // 六月 - 初夏时节
        { name: '栀子花', styles: ['水彩', '国画', '油画'] },
        { name: '茉莉花', styles: ['工笔画', '水彩', '国画'] },
        { name: '荷花', styles: ['国画', '工笔画', '水墨'] },
        { name: '月季', styles: ['油画', '水彩', '版画'] }
    ],
    7: [ // 七月 - 盛夏
        { name: '荷花', styles: ['国画', '工笔画', '水墨'] },
        { name: '向日葵', styles: ['油画', '版画', '水彩'] },
        { name: '茉莉花', styles: ['工笔画', '水彩', '国画'] },
        { name: '紫薇', styles: ['国画', '水彩', '工笔画'] }
    ],
    8: [ // 八月 - 夏末
        { name: '荷花', styles: ['国画', '工笔画', '水墨'] },
        { name: '向日葵', styles: ['油画', '版画', '水彩'] },
        { name: '紫薇', styles: ['国画', '水彩', '工笔画'] },
        { name: '木槿', styles: ['水彩', '国画', '油画'] }
    ],
    9: [ // 九月 - 金秋时节
        { name: '桂花', styles: ['工笔画', '国画', '水彩'] },
        { name: '菊花', styles: ['国画', '工笔画', '水墨'] },
        { name: '木芙蓉', styles: ['国画', '水彩', '工笔画'] }
    ],
    10: [ // 十月 - 深秋
        { name: '菊花', styles: ['国画', '工笔画', '水墨'] },
        { name: '桂花', styles: ['工笔画', '国画', '水彩'] },
        { name: '木芙蓉', styles: ['国画', '水彩', '工笔画'] },
        { name: '海棠果', styles: ['水彩', '国画', '油画'] }
    ],
    11: [ // 十一月 - 晚秋
        { name: '菊花', styles: ['国画', '工笔画', '水墨'] },
        { name: '山茶花', styles: ['油画', '工笔画', '国画'] },
        { name: '木芙蓉', styles: ['国画', '水彩', '工笔画'] }
    ],
    12: [ // 十二月 - 初冬
        { name: '梅花', styles: ['国画', '水墨', '写意'] },
        { name: '山茶花', styles: ['油画', '工笔画', '国画'] },
        { name: '水仙', styles: ['工笔画', '国画', '水彩'] }
    ]
};

// Gemini API 配置和函数
let useAiGeneration = true;  // 重新启用AI生成
let geminiApiKey = 'AIzaSyDZRfmpBIMHeG_r9lx4ftaSWnP_p985JtI';

// API端点配置
const API_ENDPOINTS = {
    // 原始Google API（图像生成正常）
    google: 'https://generativelanguage.googleapis.com/v1beta',
    // CLAWCLOUD中转API（文本正常，图像有问题）
    clawcloud: 'https://iebemjnrzxnv.ap-southeast-1.clawcloudrun.com/V1'
};

// 图片托管服务配置
const IMAGE_HOSTING = {
    unsplash: 'Unsplash免费图片',
    cloudflare: 'Cloudflare Images',
    picgo: 'PicGo图床'
};

// 当前使用的API端点
let currentApiEndpoint = API_ENDPOINTS.google;  // 默认使用原始Google API

// 图片来源切换函数
function switchImageSource(source) {
    const statusElement = document.getElementById('api-status');

    if (source === 'google-ai') {
        statusElement.textContent = '✅ 完全免费';
        statusElement.style.background = '#e8f5e8';
        statusElement.style.color = '#2e7d32';
        console.log('🎨 切换到Google AI生成图片');
        // 重新启用AI生成
        useAiGeneration = true;
    } else if (source === 'unsplash') {
        statusElement.textContent = '✅ 完全免费';
        statusElement.style.background = '#e8f5e8';
        statusElement.style.color = '#2e7d32';
        console.log('🌸 切换到Unsplash真实花卉');
        // 禁用AI生成，使用Unsplash
        useAiGeneration = false;
    }
}

// 添加API请求控制变量
let lastApiRequestTime = 0;
const API_REQUEST_INTERVAL = 10000; // 10秒内不允许多次请求
let cooldownTimer = null;

// 添加配额状态管理函数
function updateQuotaStatus(isExceeded) {
    const statusElement = document.getElementById('quota-status');
    if (statusElement) {
        if (isExceeded) {
            statusElement.textContent = '配额超限';
            statusElement.className = 'exceeded';
        } else {
            statusElement.textContent = '正常';
            statusElement.className = '';
        }
    }
}

// 更新刷新冷却时间
function updateCooldownTimer() {
    const cooldownElement = document.getElementById('cooldown-time');
    if (!cooldownElement) return;
    
    // 清除之前的定时器
    if (cooldownTimer) {
        clearInterval(cooldownTimer);
    }
    
    cooldownTimer = setInterval(() => {
        const currentTime = Date.now();
        const timeSinceLastRequest = currentTime - lastApiRequestTime;
        const remainingTime = Math.max(0, Math.ceil((API_REQUEST_INTERVAL - timeSinceLastRequest) / 1000));
        
        if (remainingTime <= 0) {
            cooldownElement.textContent = '可以刷新';
            cooldownElement.style.color = '#4CAF50';
            clearInterval(cooldownTimer);
        } else {
            cooldownElement.textContent = `${remainingTime}秒后可刷新`;
            cooldownElement.style.color = '#f44336';
        }
    }, 1000);
}

// 初始化Gemini配置
function initGeminiConfig() {
    // 从存储中加载API密钥和生成模式
    chrome.storage.sync.get(['geminiApiKey', 'useAiGeneration'], (result) => {
        if (result.geminiApiKey) {
            geminiApiKey = result.geminiApiKey;
        } else {
            // 如果存储中没有API密钥，使用默认值并保存
            chrome.storage.sync.set({ geminiApiKey: geminiApiKey });
        }
        document.getElementById('gemini-api-key').value = geminiApiKey;
        
        if (result.useAiGeneration !== undefined) {
            useAiGeneration = result.useAiGeneration;
        } else {
            // 如果存储中没有设置，使用默认值并保存
            chrome.storage.sync.set({ useAiGeneration: useAiGeneration });
        }
        document.getElementById('toggle-ai-generation').textContent = 
            useAiGeneration ? '使用占位图' : '使用AI生成';
    });

    // 保存API密钥
    document.getElementById('save-gemini-key').addEventListener('click', () => {
        const newKey = document.getElementById('gemini-api-key').value.trim();
        if (newKey) {
            geminiApiKey = newKey;
            chrome.storage.sync.set({ geminiApiKey: newKey });
            alert('API密钥已保存');
        }
    });

    // 切换生成模式
    document.getElementById('toggle-ai-generation').addEventListener('click', () => {
        useAiGeneration = !useAiGeneration;
        chrome.storage.sync.set({ useAiGeneration });
        document.getElementById('toggle-ai-generation').textContent = 
            useAiGeneration ? '使用占位图' : '使用AI生成';
        // 重新生成当前花卉图片
        updateFlowerDisplay(currentDate);
    });
}

// 使用Gemini API生成花卉图片
async function generateFlowerImage(flower, style) {
    if (!geminiApiKey) {
        console.warn('未设置Gemini API密钥');
        return null;
    }

    // 添加请求频率限制
    const currentTime = Date.now();
    if (currentTime - lastApiRequestTime < API_REQUEST_INTERVAL) {
        console.warn(`API请求过于频繁，需要等待至少${(API_REQUEST_INTERVAL/1000).toFixed(1)}秒。使用备用图像。`);
        return null;
    }
    
    const date = new Date();
    const day = date.getDate();
    const month = getMonth(date);

    // 根据月份生成不同的诗意描述
    const monthDesc = {
        1: "寒冬腊月，梅花傲雪",
        2: "立春时节，万物萌动",
        3: "春暖花开，桃红柳绿",
        4: "暮春时节，牡丹盛开",
        5: "晚春初夏，芍药飘香",
        6: "初夏时节，栀子花开",
        7: "盛夏时光，荷花满塘",
        8: "夏末秋初，紫薇绽放",
        9: "金秋时节，桂花飘香",
        10: "深秋时分，菊花满园",
        11: "晚秋时节，霜叶如花",
        12: "初冬时光，山茶花开"
    };

    const prompt = `今天是${month}月${day}日，${monthDesc[month]}。请为我创作一幅充满诗意的${flower.name}艺术画：
    1. 画面主题：一朵盛开的${flower.name}，体现${month}月的节令特色
    2. 艺术风格：${style}
    3. 要求：构图优美，意境深远，体现当月的自然特征
    4. 画面氛围：清新自然，富有生命力，符合节令
    5. 适合作为日历插画使用`;

    // 根据选择的API端点生成URL
    let url;
    const selectedEndpoint = document.getElementById('api-endpoint')?.value || 'google';

    if (selectedEndpoint === 'google') {
        url = `${API_ENDPOINTS.google}/models/gemini-2.0-flash-exp-image-generation:generateContent?key=${geminiApiKey}`;
        console.log(`🎨 使用原始Google API生成图像: ${flower.name} (${style}风格)`);
    } else {
        // 使用CLAWCLOUD中转API（可能有问题）
        url = `${API_ENDPOINTS.clawcloud}/images/generations`;
        console.log(`🎨 使用CLAWCLOUD中转API生成图像: ${flower.name} (${style}风格)`);
    }
    
    try {
        // 更新最后请求时间
        lastApiRequestTime = currentTime;
        
        // 更新冷却时间显示
        updateCooldownTimer();
        
        // 根据API端点使用不同的请求格式
        let requestBody, headers;

        if (selectedEndpoint === 'google') {
            // Google原始API格式
            headers = { 'Content-Type': 'application/json' };
            requestBody = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    responseModalities: ["Text", "Image"]
                }
            };
        } else {
            // CLAWCLOUD中转API格式（OpenAI兼容）
            headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${geminiApiKey}`
            };
            requestBody = {
                model: "imagen-3.0-generate-002",
                prompt: prompt,
                n: 1,
                size: "1024x1024",
                response_format: "b64_json"
            };
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            if (errorText.includes('Quota exceeded') || errorText.includes('rate limit')) {
                console.error('API配额超出限制:', errorText);
                // 在显示错误信息的地方添加提示
                const apiStatus = document.getElementById('api-status');
                if (apiStatus) {
                    apiStatus.textContent = '配额超出限制，请稍后再试';
                    apiStatus.style.color = '#f44336';
                    
                    // 5秒后清除错误消息
                    setTimeout(() => {
                        apiStatus.textContent = '';
                    }, 5000);
                }
                
                // 更新配额状态
                updateQuotaStatus(true);
                
                // 30秒后自动重置配额状态
                setTimeout(() => {
                    updateQuotaStatus(false);
                }, 30000);
                
                return null;
            }
            throw new Error(`API请求失败: ${response.status} - ${errorText}`);
        }

        // 请求成功，确保配额状态显示正常
        updateQuotaStatus(false);
        
        const data = await response.json();
        return processGeminiResponse(data);
    } catch (error) {
        console.error('生成图像时出错:', error);
        
        // 检查是否是配额错误
        if (error.message && (error.message.includes('Quota exceeded') || error.message.includes('rate limit'))) {
            const apiStatus = document.getElementById('api-status');
            if (apiStatus) {
                apiStatus.textContent = '配额超出限制，请稍后再试';
                apiStatus.style.color = '#f44336';
                
                // 5秒后清除错误消息
                setTimeout(() => {
                    apiStatus.textContent = '';
                }, 5000);
            }
            
            // 更新配额状态
            updateQuotaStatus(true);
            
            // 30秒后自动重置配额状态
            setTimeout(() => {
                updateQuotaStatus(false);
            }, 30000);
        }
        
        return null;
    }
}

// 处理Gemini API响应
function processGeminiResponse(responseData) {
    try {
        if (responseData.candidates && responseData.candidates[0]?.content?.parts) {
            const parts = responseData.candidates[0].content.parts;
            for (const part of parts) {
                if (part.inlineData) {
                    return `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`;
                }
            }
        }
        return null;
    } catch (error) {
        console.error('处理API响应时出错:', error);
        return null;
    }
}

// 修改updateFlowerDisplay函数 - 按月份选择花卉
async function updateFlowerDisplay(date) {
    console.log("更新花卉展示");
    const month = getMonth(date);
    const monthFlowers = flowers[month];

    // 在当月花卉中真正随机选择
    const flowerIndex = Math.floor(Math.random() * monthFlowers.length);
    const flower = monthFlowers[flowerIndex];
    
    // 随机选择艺术风格
    const styleIndex = Math.floor(Math.random() * flower.styles.length);
    const style = flower.styles[styleIndex];
    
    // 更新显示
    // const flowerNameElement = document.getElementById('flowerName');
    // const artStyleElement = document.getElementById('artStyle');
    const flowerImageElement = document.getElementById('flowerImage');

    // 已隐藏花卉名称和艺术风格文字显示
    // if (flowerNameElement) flowerNameElement.textContent = flower.name;
    // if (artStyleElement) artStyleElement.textContent = style;
    
    if (flowerImageElement) {
        if (useAiGeneration && geminiApiKey) {
            // 显示加载状态
            flowerImageElement.src = '';
            flowerImageElement.alt = '正在生成艺术图片...';
            
            // 使用Gemini API生成图片
            const imageUrl = await generateFlowerImage(flower, style);
            if (imageUrl) {
                flowerImageElement.src = imageUrl;
                flowerImageElement.alt = `${style}风格的${flower.name}`;
            } else {
                // 如果生成失败，使用更精确的花卉关键词
                const flowerKeywords = {
                    '牡丹': 'peony+flower+bloom',
                    '桃花': 'peach+blossom+flower',
                    '樱花': 'cherry+blossom+flower',
                    '玉兰': 'magnolia+flower+bloom',
                    '郁金香': 'tulip+flower+bloom',
                    '荷花': 'lotus+flower+bloom',
                    '向日葵': 'sunflower+bloom',
                    '栀子花': 'gardenia+flower+bloom',
                    '茉莉': 'jasmine+flower+bloom',
                    '睡莲': 'water+lily+bloom',
                    '菊花': 'chrysanthemum+flower+bloom',
                    '芙蓉': 'hibiscus+flower+bloom',
                    '桂花': 'osmanthus+flower+bloom',
                    '芦苇': 'reed+flower+nature',
                    '野菊': 'wild+chrysanthemum+flower',
                    '梅花': 'plum+blossom+flower',
                    '山茶': 'camellia+flower+bloom',
                    '水仙': 'narcissus+flower+bloom',
                    '腊梅': 'winter+sweet+flower',
                    '兰花': 'orchid+flower+bloom'
                };
                
                const keyword = flowerKeywords[flower.name] || `${flower.name}+flower+bloom`;
                const placeholderImage = `https://picsum.photos/800/600?${encodeURIComponent(keyword)}`;
                flowerImageElement.src = placeholderImage;
                flowerImageElement.alt = `${flower.name}（${style}风格意境图）`;
            }
        } else {
            // 使用免费的Unsplash API获取花卉图片
            const flowerKeywords = {
                '梅花': 'plum+blossom',
                '水仙': 'narcissus+flower',
                '山茶花': 'camellia+flower',
                '迎春花': 'winter+jasmine',
                '桃花': 'peach+blossom',
                '樱花': 'cherry+blossom',
                '玉兰': 'magnolia+flower',
                '牡丹': 'peony+flower',
                '海棠': 'crabapple+blossom',
                '杏花': 'apricot+blossom',
                '梨花': 'pear+blossom',
                '芍药': 'peony+flower',
                '月季': 'rose+flower',
                '蔷薇': 'wild+rose',
                '栀子花': 'gardenia+flower',
                '茉莉花': 'jasmine+flower',
                '荷花': 'lotus+flower',
                '向日葵': 'sunflower',
                '紫薇': 'crape+myrtle',
                '木槿': 'hibiscus+flower',
                '桂花': 'osmanthus+flower',
                '菊花': 'chrysanthemum+flower',
                '木芙蓉': 'cotton+rose',
                '海棠果': 'crabapple+fruit'
            };
            
            const keyword = flowerKeywords[flower.name] || flower.name;

            // 使用Unsplash免费API获取真实花卉图片
            const unsplashUrl = `https://source.unsplash.com/800x600/?${keyword}`;
            const fallbackUrl = `https://picsum.photos/800/600?random=${Date.now()}`;

            // 先尝试Unsplash，失败则使用Picsum
            const img = new Image();
            img.onload = function() {
                flowerImageElement.src = unsplashUrl;
                flowerImageElement.alt = `${flower.name}（${style}风格）`;
                console.log(`✅ 使用Unsplash真实花卉图片: ${flower.name}`);
            };
            img.onerror = function() {
                flowerImageElement.src = fallbackUrl;
                flowerImageElement.alt = `${flower.name}（${style}风格意境图）`;
                console.log(`⚠️ Unsplash失败，使用Picsum占位图片`);
            };
            img.src = unsplashUrl;
        }
    }
    
    console.log(`${month}月花卉: ${flower.name}, 风格: ${style}`);
}

// 测试OpenMeteo API连接
function testOpenMeteoApi() {
    console.log('开始测试OpenMeteo API连接...');
    // 更详细的测试 URL 参数，确保获取足够的数据来填充预报
    const testUrl = 'https://api.open-meteo.com/v1/forecast?latitude=39.9042&longitude=116.4074&daily=weathercode,temperature_2m_max,temperature_2m_min&forecast_days=4&timezone=auto';
    
    fetch(testUrl)
        .then(response => {
            console.log('API测试响应状态: ', response.status, response.statusText);
            if (response.ok) {
                return response.json();
            } else {
                throw new Error(`测试API失败: ${response.status} ${response.statusText}`);
            }
        })
        .then(data => {
            console.log('API测试成功, 数据: ', data);
            if (data && data.daily) {
                console.log('测试天气代码: ', data.daily.weathercode);
                console.log('测试最高温度: ', data.daily.temperature_2m_max);
                console.log('测试最低温度: ', data.daily.temperature_2m_min);
                
                // 测试转换天气代码到图标
                console.log('测试天气图标映射:');
                if (data.daily.weathercode && data.daily.weathercode.length > 0) {
                    const code = data.daily.weathercode[0];
                    const iconClass = getWeatherIconClass(code);
                    console.log(`天气代码 ${code} 对应图标类: ${iconClass}`);
                    console.log(`天气代码 ${code} 对应描述: ${getWeatherDescription(code)}`);
                }
            }
        })
        .catch(error => {
            console.error('API测试出错: ', error);
        });
}

// 在文档加载后执行初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log("文档加载完成，开始初始化...");
    
    // 立即设置当前日期
    currentDate = new Date();
    console.log("初始化当前日期:", currentDate.toLocaleString());
    
    // 更新日期显示
    updateDateDisplay(currentDate);
    
    // 初始化冷却时间显示
    updateCooldownTimer();
    
    // 设置按钮事件监听器
    const prevDateBtn = document.getElementById('prevDate');
    const todayDateBtn = document.getElementById('todayDate');
    const nextDateBtn = document.getElementById('nextDate');
    
    if (prevDateBtn) {
        prevDateBtn.addEventListener('click', () => {
            changeDate(-1);
            console.log("点击了上一天按钮");
        });
    }
    
    if (nextDateBtn) {
        nextDateBtn.addEventListener('click', () => {
            changeDate(1);
            console.log("点击了下一天按钮");
        });
    }
    
    if (todayDateBtn) {
        todayDateBtn.addEventListener('click', () => {
            resetToToday();
            console.log("点击了今天按钮");
        });
    }
    
    // 添加键盘事件监听
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            changeDate(-1);
            console.log("按下左箭头键");
        } else if (e.key === 'ArrowRight') {
            changeDate(1);
            console.log("按下右箭头键");
        }
    });
    
    // 获取初始天气数据
    fetchWeatherData(currentDate);
    
    // 初始化花卉显示
    updateFlowerDisplay(currentDate);

    // 初始化Gemini配置
    initGeminiConfig();

    // 设置图片来源选择器事件
    const imageSourceSelect = document.getElementById('image-source');
    if (imageSourceSelect) {
        imageSourceSelect.addEventListener('change', (e) => {
            switchImageSource(e.target.value);
            // 重新更新花卉显示
            updateFlowerDisplay(currentDate);
        });
        // 初始化状态 - 默认使用Google AI
        switchImageSource('google-ai');
    }

    // 设置按钮点击事件
    const settingsBtn = document.getElementById('settingsBtn');
    const settingsPanel = document.getElementById('settingsPanel');
    
    if (settingsBtn && settingsPanel) {
        settingsBtn.addEventListener('click', () => {
            settingsPanel.classList.toggle('active');
        });
        
        // 点击面板外关闭面板
        document.addEventListener('click', (e) => {
            if (!settingsPanel.contains(e.target) && !settingsBtn.contains(e.target)) {
                settingsPanel.classList.remove('active');
            }
        });
    }
});

// 强制清除静态内容的函数
function forceClearStaticContent() {
    console.log("清除HTML中的静态内容");
    
    // 获取所有需要清除的元素
    const monthYearElement = document.getElementById('monthYear');
    const dayNumberElement = document.getElementById('dayNumber');
    const weekdayElement = document.getElementById('weekdayDisplay');
    const lunarDateElement = document.getElementById('lunarDate');
    const holidayInfoElement = document.getElementById('holidayInfo');
    const temperatureElement = document.getElementById('temperature');
    const weatherDescElement = document.getElementById('weather-description');
    const weatherIconElement = document.getElementById('weather-icon');
    const forecastContainer = document.getElementById('forecast-container');
    
    // 清除内容
    if (monthYearElement) monthYearElement.textContent = '';
    if (dayNumberElement) dayNumberElement.textContent = '';
    if (weekdayElement) weekdayElement.textContent = '';
    if (lunarDateElement) lunarDateElement.textContent = '';
    if (holidayInfoElement) {
        holidayInfoElement.innerHTML = '';
        holidayInfoElement.style.display = 'none';
    }
    if (temperatureElement) temperatureElement.textContent = '加载中...';
    if (weatherDescElement) weatherDescElement.textContent = '加载中...';
    if (weatherIconElement) weatherIconElement.innerHTML = '<i class="wi wi-na"></i>';
    if (forecastContainer) forecastContainer.innerHTML = '';
}

// 处理地点变更
function handleLocationChange(event) {
    const selectedLocation = event.target.value;
    console.log(`用户选择了新地点: ${selectedLocation}`);
    
    if (locations[selectedLocation]) {
        userCity = selectedLocation;
        userLocation = locations[selectedLocation];
        console.log(`切换到新位置: ${userLocation.name}, 坐标(${userLocation.lat}, ${userLocation.lon})`);
        
        // 强制清除所有缓存数据
        clearWeatherCache();
        
        // 强制清除静态内容
        forceClearStaticContent();
        
        // 强制更新日期显示
        updateDateDisplay(currentDate);
        
        // 重新获取天气数据
        fetchWeatherData(currentDate);
        
        // 添加动画效果
        const card = document.querySelector('.date-weather-card');
        if (card) {
            card.style.opacity = '0.5';
            setTimeout(() => {
                card.style.opacity = '1';
            }, 300);
        }
    } else {
        console.error(`未找到地点 "${selectedLocation}" 的坐标信息`);
    }
}

// 处理刷新按钮点击
function handleRefresh() {
    console.log("用户点击了刷新按钮");
    
    // 检查是否在冷却期内
    const currentTime = Date.now();
    if (currentTime - lastApiRequestTime < API_REQUEST_INTERVAL) {
        const remainingTime = Math.ceil((API_REQUEST_INTERVAL - (currentTime - lastApiRequestTime)) / 1000);
        console.log(`刷新在冷却中，还需等待 ${remainingTime} 秒`);
        
        // 显示提示
        const apiStatus = document.getElementById('api-status');
        if (apiStatus) {
            apiStatus.textContent = `请等待 ${remainingTime} 秒后再刷新`;
            apiStatus.style.color = '#f44336';
            
            // 5秒后清除错误消息
            setTimeout(() => {
                apiStatus.textContent = '';
            }, 3000);
        }
        
        // 添加一个轻微震动效果而不是旋转
        const refreshIcon = document.querySelector('#refreshButton i');
        refreshIcon.classList.add('shake');
        setTimeout(() => {
            refreshIcon.classList.remove('shake');
        }, 500);
        
        return;
    }
    
    // 添加旋转动画
    const refreshIcon = document.querySelector('#refreshButton i');
    refreshIcon.classList.add('rotating');
    
    // 清除缓存中的天气数据
    clearWeatherCache();
    
    // 重新获取天气数据
    fetchWeatherData(currentDate);
    
    // 更新花卉显示
    updateFlowerDisplay(currentDate);
    
    // 更新最后请求时间
    lastApiRequestTime = currentTime;
    
    // 更新冷却时间显示
    updateCooldownTimer();
    
    // 5秒后移除旋转动画
    setTimeout(() => {
        refreshIcon.classList.remove('rotating');
    }, 1000);
}

// 清除所有天气缓存数据
function clearWeatherCache() {
    console.log("清除天气数据缓存");
    
    // 遍历sessionStorage，删除所有以"weather_"开头的项
    Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('weather_')) {
            console.log(`从缓存中移除: ${key}`);
            sessionStorage.removeItem(key);
        }
    });
}

// 更新日期和天气显示
function updateDateDisplay(date) {
    console.log(`执行updateDateDisplay，参数date:`, date);
    
    try {
        // 确保日期对象是有效的
        if (!(date instanceof Date) || isNaN(date.getTime())) {
            console.error("无效的日期对象，重置为当前日期");
            date = new Date();
        }
        
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // 月份从0开始，需要+1
        const day = date.getDate();
        const weekday = date.getDay();
        
        console.log(`日期组成: 年=${year}, 月=${month}, 日=${day}, 星期=${weekday}`);
        
        // 获取DOM元素
        const monthYearElement = document.getElementById('monthYear');
        const dayNumberElement = document.getElementById('dayNumber');
        const weekdayElement = document.getElementById('weekdayDisplay');
        const lunarDateElement = document.getElementById('lunarDate');
        const holidayInfoElement = document.getElementById('holidayInfo');
        
        // 记录DOM元素是否存在
        console.log(`DOM元素存在检查:`, {
            monthYearElement: !!monthYearElement,
            dayNumberElement: !!dayNumberElement,
            weekdayElement: !!weekdayElement,
            lunarDateElement: !!lunarDateElement,
            holidayInfoElement: !!holidayInfoElement
        });
        
        // 更新日期显示
        if (monthYearElement) {
            monthYearElement.textContent = `${year}年${month}月`;
            console.log(`更新显示: monthYear=${year}年${month}月`);
        } else {
            console.error("未找到monthYear元素");
        }
        
        if (dayNumberElement) {
            dayNumberElement.textContent = day;
            console.log(`更新显示: dayNumber=${day}`);
        } else {
            console.error("未找到dayNumber元素");
        }
        
        if (weekdayElement) {
            weekdayElement.textContent = weekdayNames[weekday];
            console.log(`更新显示: weekday=${weekdayNames[weekday]}`);
        } else {
            console.error("未找到weekdayDisplay元素");
        }
        
        // 计算农历（更准确的简化版本）
        // 这里我们选择一个接近真实农历的算法，而不是完全随机生成
        // 注意：这仍然是近似值，不保证与实际农历完全一致
        const dayOfYear = Math.floor((date - new Date(date.getFullYear(), 0, 0)) / 86400000);
        const lunarYearOffset = (year - 1900) % 19; // 农历周期大约是19年
        const lunarMonthOffset = (month + lunarYearOffset + Math.floor(day / 10)) % 12;
        const lunarDayOffset = (day + dayOfYear % 30) % 30;
        
        const lunarMonthName = lunarInfo.monthNames[lunarMonthOffset];
        const lunarDayName = lunarInfo.dayNames[lunarDayOffset];
        
        if (lunarDateElement) {
            lunarDateElement.textContent = `${lunarMonthName}${lunarDayName}`;
            console.log(`更新显示: lunarDate=${lunarMonthName}${lunarDayName}`);
        } else {
            console.error("未找到lunarDate元素");
        }
        
        // 更新节日信息
        const holidayKey = `${month}-${day}`;
        if (holidays[holidayKey]) {
            holidayInfoElement.innerHTML = `<i class="fas fa-gift"></i><span>${holidays[holidayKey]}</span>`;
            holidayInfoElement.style.display = 'block';
            console.log(`今天是特殊节日: ${holidays[holidayKey]}`);
        } else {
            holidayInfoElement.style.display = 'none';
            console.log("今天不是特殊节日");
        }
    } catch (error) {
        console.error("更新日期显示时出错:", error);
    }
}

// 显示后备天气信息
function showFallbackWeather() {
    console.log("显示后备天气信息");
    
    // 获取DOM元素
    const weatherIconElement = document.getElementById('weather-icon');
    const temperatureElement = document.getElementById('temperature');
    const weatherDescElement = document.getElementById('weather-description');
    
    // 确保在显示wi-na图标时，温度和描述也显示为"不可用"状态
    if (weatherIconElement) {
        weatherIconElement.innerHTML = '';
        const iconElement = document.createElement('i');
        iconElement.className = 'wi wi-na';
        weatherIconElement.appendChild(iconElement);
    }
    
    if (temperatureElement) {
        temperatureElement.textContent = '--°C';
    }
    
    if (weatherDescElement) {
        weatherDescElement.textContent = '数据暂不可用';
    }
    
    // 清空预报
    const forecastContainer = document.getElementById('forecast-container');
    if (forecastContainer) {
        forecastContainer.innerHTML = '';
    }
    
    // 如果是开发环境，显示演示数据
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.protocol === 'file:') {
        showDemoWeather();
    }
}

// 显示演示天气数据
function showDemoWeather() {
    console.log("显示演示天气数据");
    
    // 创建模拟数据
    const demoData = {
        cityName: "北京",
        date: new Date().toDateString(),
        weatherCode: 0, // 晴天
        maxTemp: 15,
        minTemp: 5,
        timestamp: new Date().toISOString(),
        forecast: []
    };
    
    // 添加三天的预报数据
    for (let i = 1; i <= 3; i++) {
        const forecastDate = new Date();
        forecastDate.setDate(forecastDate.getDate() + i);
        
        demoData.forecast.push({
            date: forecastDate.toDateString(),
            weatherCode: i % 3 === 0 ? 61 : (i % 2 === 0 ? 2 : 0), // 晴天、多云、小雨
            maxTemp: 15 - i,
            minTemp: 5 - i
        });
    }
    
    // 更新显示
    updateWeatherDisplay(demoData);
    console.log("演示天气数据已显示");
}

// 获取天气数据
async function fetchWeatherData(date) {
    if (!locations || !userCity || !locations[userCity]) {
        console.error("未找到地点配置或用户城市设置");
        showFallbackWeather();
        return;
    }

    try {
        // 打印当前位置信息
        console.log("当前位置配置:", {
            city: userCity,
            location: locations[userCity],
            date: date.toLocaleString()
        });

        const lat = locations[userCity].lat;
        const lon = locations[userCity].lon;
        
        // 构建API URL，添加时间戳防止缓存
        const timestamp = new Date().getTime();
        const weatherApiUrl = `https://api.open-meteo.com/v1/forecast?latitude=${lat}&longitude=${lon}&daily=weathercode,temperature_2m_max,temperature_2m_min&forecast_days=4&timezone=Asia/Shanghai&current_weather=true&_=${timestamp}`;
        console.log("请求URL:", weatherApiUrl);
        
        // 发起请求前打印信息
        console.log("开始发送API请求...");
        
        // 发起请求，添加no-cache头
        const response = await fetch(weatherApiUrl, {
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        
        console.log("API响应状态:", response.status, response.statusText);
        
        // 检查响应头
        const headers = {};
        for (const [key, value] of response.headers) {
            headers[key] = value;
        }
        console.log("响应头:", headers);
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log("原始API返回数据:", JSON.stringify(data, null, 2));
        
        // 验证数据结构
        if (!data.daily) {
            console.error("API返回数据缺少daily字段");
            throw new Error("无效的API响应格式");
        }
        
        console.log("daily数据:", {
            weathercodes: data.daily.weathercode,
            maxTemps: data.daily.temperature_2m_max,
            minTemps: data.daily.temperature_2m_min
        });
        
        // 处理数据
        const weatherData = {
            cityName: locations[userCity].name,
            date: date.toDateString(),
            weatherCode: parseInt(data.daily.weathercode[0]),
            maxTemp: Math.round(parseFloat(data.daily.temperature_2m_max[0])),
            minTemp: Math.round(parseFloat(data.daily.temperature_2m_min[0])),
            timestamp: new Date().toISOString(),
            forecast: []
        };
        
        console.log("处理后的今日天气数据:", {
            city: weatherData.cityName,
            date: weatherData.date,
            weatherCode: weatherData.weatherCode,
            maxTemp: weatherData.maxTemp,
            minTemp: weatherData.minTemp,
            description: getWeatherDescription(weatherData.weatherCode)
        });
        
        // 添加预报数据
        for (let i = 1; i < 4 && i < data.daily.weathercode.length; i++) {
            const forecastDate = new Date(date);
            forecastDate.setDate(forecastDate.getDate() + i);
            
            const forecastEntry = {
                date: forecastDate.toDateString(),
                weatherCode: parseInt(data.daily.weathercode[i]),
                maxTemp: Math.round(parseFloat(data.daily.temperature_2m_max[i])),
                minTemp: Math.round(parseFloat(data.daily.temperature_2m_min[i]))
            };
            
            weatherData.forecast.push(forecastEntry);
            
            console.log(`第 ${i} 天预报数据:`, {
                date: forecastEntry.date,
                weatherCode: forecastEntry.weatherCode,
                maxTemp: forecastEntry.maxTemp,
                minTemp: forecastEntry.minTemp,
                description: getWeatherDescription(forecastEntry.weatherCode)
            });
        }
        
        // 更新显示
        console.log("开始更新显示...");
        updateWeatherDisplay(weatherData);
        console.log("显示更新完成");
        
    } catch (error) {
        console.error("获取天气数据时出错:", error);
        console.error("错误堆栈:", error.stack);
        showFallbackWeather();
    }
}

// 处理 OpenMeteo 当前天气数据
function processOpenMeteoCurrent(data) {
    console.log("处理当前天气数据", data);
    
    try {
        // 检查数据有效性
        if (!data || !data.daily || !data.daily.weathercode || data.daily.weathercode.length === 0) {
            console.error("无效的天气数据格式");
            showFallbackWeather();
            return;
        }
        
        // 获取今天的天气代码和温度
        const weatherCode = data.daily.weathercode[0];
        const maxTemp = data.daily.temperature_2m_max[0];
        const minTemp = data.daily.temperature_2m_min[0];
        
        // 计算平均温度作为当前温度
        const currentTemp = Math.round((maxTemp + minTemp) / 2);
        
        console.log(`当前天气: 代码=${weatherCode}, 最高温度=${maxTemp}, 最低温度=${minTemp}, 平均温度=${currentTemp}`);
        
        // 从天气代码获取描述
        const weatherDescription = getWeatherDescription(weatherCode);
        
        // 从天气代码获取图标
        const weatherIcon = getWeatherIcon(weatherCode);
        
        // 更新DOM
        const temperatureElement = document.getElementById('temperature');
        const weatherDescriptionElement = document.getElementById('weather-description');
        const weatherIconElement = document.getElementById('weather-icon');
        
        // 如果天气图标是"不可用"，就不显示温度
        if (weatherIcon === 'wi-na') {
            if (temperatureElement) temperatureElement.textContent = '--°C';
            if (weatherDescriptionElement) weatherDescriptionElement.textContent = '数据暂不可用';
        } else {
            if (temperatureElement) {
                temperatureElement.textContent = `${currentTemp}°C`;
            }
            
            if (weatherDescriptionElement) {
                weatherDescriptionElement.textContent = weatherDescription;
            }
        }
        
        // 更新图标统一使用DOM方式
        if (weatherIconElement) {
            weatherIconElement.innerHTML = '';
            const iconElement = document.createElement('i');
            iconElement.className = `wi ${weatherIcon}`;
            weatherIconElement.appendChild(iconElement);
        }
        
        console.log(`天气信息已更新: 温度=${currentTemp}°C, 描述=${weatherDescription}, 图标=${weatherIcon}`);
    } catch (error) {
        console.error("处理当前天气数据时出错:", error);
        showFallbackWeather();
    }
}

// 将 OpenMeteo 天气代码映射到图标类
function getWeatherIcon(code) {
    // 如果代码是null或undefined，直接返回'wi-na'
    if (code === null || code === undefined) {
        return 'wi-na';
    }
    
    const codeMap = {
        0: 'wi-day-sunny', // 晴天
        1: 'wi-day-sunny-overcast', // 大部晴朗
        2: 'wi-day-cloudy', // 部分多云
        3: 'wi-cloudy', // 多云
        45: 'wi-fog', // 雾
        48: 'wi-fog', // 霾
        51: 'wi-sprinkle', // 小毛毛雨
        53: 'wi-sprinkle', // 毛毛雨
        55: 'wi-sprinkle', // 密集毛毛雨
        56: 'wi-rain-mix', // 轻度冻雨
        57: 'wi-rain-mix', // 密集冻雨
        61: 'wi-showers', // 小雨
        63: 'wi-rain', // 中雨
        65: 'wi-rain', // 大雨
        66: 'wi-rain-mix', // 轻度冻雨
        67: 'wi-rain-mix', // 强冻雨
        71: 'wi-snow', // 小雪
        73: 'wi-snow', // 中雪
        75: 'wi-snow', // 大雪
        77: 'wi-snow', // 雪粒
        80: 'wi-showers', // 小阵雨
        81: 'wi-rain', // 中阵雨
        82: 'wi-rain', // 强阵雨
        85: 'wi-snow', // 小阵雪
        86: 'wi-snow', // 强阵雪
        95: 'wi-thunderstorm', // 雷暴
        96: 'wi-thunderstorm', // 雷暴伴有小冰雹
        99: 'wi-thunderstorm' // 雷暴伴有大冰雹
    };
    
    return codeMap[code] || 'wi-na'; // 默认图标为 'wi-na'
}

// 获取中文天气描述
function getWeatherDescription(code) {
    const descriptionMap = {
        0: '晴天',
        1: '大部晴朗',
        2: '部分多云',
        3: '多云',
        45: '雾',
        48: '雾霾',
        51: '小毛毛雨',
        53: '毛毛雨',
        55: '密集毛毛雨',
        56: '轻度冻雨',
        57: '密集冻雨',
        61: '小雨',
        63: '中雨',
        65: '大雨',
        66: '轻度冻雨',
        67: '强冻雨',
        71: '小雪',
        73: '中雪',
        75: '大雪',
        77: '雪粒',
        80: '小阵雨',
        81: '中阵雨',
        82: '强阵雨',
        85: '小阵雪',
        86: '强阵雪',
        95: '雷暴',
        96: '雷暴伴有小冰雹',
        99: '雷暴伴有大冰雹'
    };
    
    return descriptionMap[code] || '未知天气'; // 默认为"未知天气"
}

// 处理OpenMeteo预报数据
function processOpenMeteoForecast(data, dayIndex) {
    console.log(`处理预报数据，天数索引:${dayIndex}`, data);
    
    try {
        // 检查数据有效性
        if (!data || !data.daily || !data.daily.weathercode || 
            !data.daily.weathercode[dayIndex] || 
            !data.daily.temperature_2m_max[dayIndex] || 
            !data.daily.temperature_2m_min[dayIndex]) {
            console.error(`预报数据无效或索引 ${dayIndex} 超出范围`);
            showFallbackWeather();
            return;
        }
        
        // 获取特定日期的预报数据
        const weatherCode = data.daily.weathercode[dayIndex];
        const maxTemp = Math.round(data.daily.temperature_2m_max[dayIndex]);
        const minTemp = Math.round(data.daily.temperature_2m_min[dayIndex]);
        
        console.log(`预报天气: 天数=${dayIndex}, 代码=${weatherCode}, 最高温度=${maxTemp}, 最低温度=${minTemp}`);
        
        // 获取天气描述和图标
        const weatherDescription = getWeatherDescription(weatherCode);
        const weatherIcon = getWeatherIcon(weatherCode);
        
        // 更新DOM
        const temperatureElement = document.getElementById('temperature');
        const weatherDescriptionElement = document.getElementById('weather-description');
        const weatherIconElement = document.getElementById('weather-icon');
        
        // 如果天气图标是"不可用"，就不显示温度
        if (weatherIcon === 'wi-na') {
            if (temperatureElement) temperatureElement.textContent = '--°C';
            if (weatherDescriptionElement) weatherDescriptionElement.textContent = '数据暂不可用';
        } else {
            if (temperatureElement) {
                temperatureElement.textContent = `${minTemp}~${maxTemp}°C`;
            }
            
            if (weatherDescriptionElement) {
                weatherDescriptionElement.textContent = weatherDescription;
            }
        }
        
        // 更新图标统一使用DOM方式
        if (weatherIconElement) {
            weatherIconElement.innerHTML = '';
            const iconElement = document.createElement('i');
            iconElement.className = `wi ${weatherIcon}`;
            weatherIconElement.appendChild(iconElement);
        }
        
        console.log(`预报信息已更新: 温度=${minTemp}~${maxTemp}°C, 描述=${weatherDescription}, 图标=${weatherIcon}`);
    } catch (error) {
        console.error("处理预报数据时出错:", error);
        showFallbackWeather();
    }
}

// 更新预报显示
function updateOpenMeteoForecastDisplay(data) {
    console.log("更新预报显示", data);
    
    try {
        const forecastContainer = document.getElementById('forecast-container');
        if (!forecastContainer) {
            console.warn("找不到预报容器元素");
            return;
        }
        
        // 清除现有预报
        forecastContainer.innerHTML = '';
        
        // 检查数据有效性
        if (!data || !data.daily || !data.daily.weathercode || data.daily.weathercode.length <= 1) {
            console.warn("预报数据不足");
            return;
        }
        
        // 显示接下来3天的预报（如果有足够数据）
        const maxDays = Math.min(4, data.daily.weathercode.length); // 最多显示4天（今天+3天）
        
        // 从第二天开始显示（索引1），因为第一天（索引0）是当天
        for (let i = 1; i < maxDays; i++) {
            const forecastDate = new Date();
            forecastDate.setDate(forecastDate.getDate() + i);
            
            const weatherCode = data.daily.weathercode[i];
            const maxTemp = Math.round(data.daily.temperature_2m_max[i]);
            const minTemp = Math.round(data.daily.temperature_2m_min[i]);
            
            // 获取天气描述和图标
            const weatherDescription = getWeatherDescription(weatherCode);
            const weatherIcon = getWeatherIcon(weatherCode);
            
            // 创建预报项元素
            const forecastItem = document.createElement('div');
            forecastItem.className = 'forecast-item';
            
            // 设置日期名称
            let dayName;
            if (i === 1) {
                dayName = '明天';
            } else if (i === 2) {
                dayName = '后天';
            } else {
                dayName = `${forecastDate.getDate()}日`;
            }
            
            // 构建预报内容 - 根据图标决定温度显示
            let tempDisplay = weatherIcon === 'wi-na' ? '--~--°C' : `${minTemp}~${maxTemp}°C`;
            let descDisplay = weatherIcon === 'wi-na' ? '数据暂不可用' : weatherDescription;
            
            forecastItem.innerHTML = `
                <div class="forecast-day">${dayName}</div>
                <div class="forecast-icon"><i class="wi ${weatherIcon}"></i></div>
                <div class="forecast-temp">${tempDisplay}</div>
                <div class="forecast-desc">${descDisplay}</div>
            `;
            
            // 添加到容器
            forecastContainer.appendChild(forecastItem);
            
            console.log(`添加预报: 日期=${forecastDate.getDate()}日, 温度=${tempDisplay}, 描述=${descDisplay}`);
        }
    } catch (error) {
        console.error("更新预报显示时出错:", error);
    }
}

// 处理日期变更
function changeDate(days) {
    console.log(`更改日期: ${days}天`);
    // 创建新日期对象避免引用问题
    const newDate = new Date(currentDate);
    newDate.setDate(newDate.getDate() + days);
    currentDate = newDate;
    
    // 立即更新日期显示
    updateDateDisplay(currentDate);
    
    // 强制清除缓存的天气数据
    clearWeatherCache();
    
    // 获取新日期的天气数据
    fetchWeatherData(currentDate);
    
    // 更新花卉显示
    updateFlowerDisplay(currentDate);
    
    console.log("日期已更新为:", currentDate.toLocaleString());
}

// 重置到今天
function resetToToday() {
    console.log("重置到今天");
    currentDate = new Date();
    
    // 立即更新日期显示
    updateDateDisplay(currentDate);
    
    // 强制清除缓存的天气数据
    clearWeatherCache();
    
    // 获取今天的天气数据
    fetchWeatherData(currentDate);
    
    // 更新花卉显示
    updateFlowerDisplay(currentDate);
    
    console.log("已重置到今天:", currentDate.toLocaleString());
}

// 带动画效果地更新日期
function updateDateWithAnimation() {
    const card = document.querySelector('.date-weather-card');
    
    // 添加淡出效果
    card.style.opacity = '0.5';
    card.style.transform = 'translateY(-10px)';
    
    // 确保currentDate是有效日期
    if (!(currentDate instanceof Date) || isNaN(currentDate.getTime())) {
        console.error("无效的当前日期，重置为现在");
        currentDate = new Date();
    }
    
    // 等待动画完成后更新内容
    setTimeout(() => {
        updateDateDisplay(currentDate);
        
        // 获取新日期的天气数据
        fetchWeatherData(currentDate);
        
        // 添加淡入效果
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, 200);
}

// 创建地区选择器
function createLocationSelector() {
    console.log("地区选择器已经在HTML中定义好了，不需要创建");
}

// 更新天气显示
function updateWeatherDisplay(weatherData) {
    console.log("开始更新天气显示，数据:", weatherData);
    
    // 获取并记录元素状态
    const weatherIcon = document.getElementById('weather-icon');
    const temperature = document.getElementById('temperature');
    const weatherDescription = document.getElementById('weather-description');
    const forecastContainer = document.getElementById('forecast-container');
    
    if (!weatherIcon || !temperature || !weatherDescription || !forecastContainer) {
        console.error("无法找到所需的天气显示元素");
        return;
    }
    
    // 更新主要天气信息
    const iconClass = getWeatherIconClass(weatherData.weatherCode);
    console.log("主要天气图标类:", iconClass);
    
    // 直接设置图标的HTML内容
    weatherIcon.innerHTML = `<i class="wi ${iconClass}"></i>`;
    console.log("设置后的天气图标HTML:", weatherIcon.innerHTML);
    
    // 修改温度显示格式，同时显示最高温和最低温
    temperature.textContent = `${weatherData.maxTemp}° / ${weatherData.minTemp}°`;
    weatherDescription.textContent = getWeatherDescription(weatherData.weatherCode);
    
    // 更新预报
    console.log("开始更新预报显示");
    forecastContainer.innerHTML = '';  // 清空现有预报
    
    weatherData.forecast.forEach((forecast, index) => {
        const forecastItem = document.createElement('div');
        forecastItem.className = 'forecast-item';
        
        const forecastIconClass = getWeatherIconClass(forecast.weatherCode);
        console.log(`第 ${index + 1} 天预报图标类:`, forecastIconClass);
        
        forecastItem.innerHTML = `
            <div class="forecast-date">${new Date(forecast.date).toLocaleDateString('zh-CN', {weekday: 'short'})}</div>
            <div class="forecast-icon">
                <i class="wi ${forecastIconClass}"></i>
            </div>
            <div class="forecast-temp">${forecast.maxTemp}° / ${forecast.minTemp}°</div>
        `;
        
        forecastContainer.appendChild(forecastItem);
        console.log(`已添加第 ${index + 1} 天预报项，温度：${forecast.maxTemp}° / ${forecast.minTemp}°`);
    });
    
    console.log("天气显示更新完成");
}

// 更新预报显示
function updateForecastDisplay(forecastData) {
    if (!forecastData || !Array.isArray(forecastData) || forecastData.length === 0) {
        console.log("没有预报数据可显示");
        return;
    }
    
    console.log("更新预报数据:", forecastData);
    
    // 使用getElementById来匹配HTML中的id
    const forecastContainer = document.getElementById('forecast-container');
    if (!forecastContainer) {
        console.error("未找到预报容器元素(id='forecast-container')");
        // 尝试使用class选择器
        const altContainer = document.querySelector('.forecast');
        if (altContainer) {
            console.log("找到替代容器: .forecast");
            altContainer.innerHTML = '';
            // 为每个预报日期创建一个预报项
            forecastData.forEach((forecast, index) => {
                try {
                    const forecastDate = new Date(forecast.date);
                    const dayName = weekdayNames[forecastDate.getDay()].replace('星期', '');
                    
                    const forecastItem = document.createElement('div');
                    forecastItem.className = 'forecast-item';
                    
                    const iconClass = getWeatherIconClass(forecast.weatherCode);
                    const weatherDescription = getWeatherDescription(forecast.weatherCode);
                    
                    forecastItem.innerHTML = `
                        <div class="forecast-day">${dayName}</div>
                        <div class="forecast-icon"><i class="wi ${iconClass}"></i></div>
                        <div class="forecast-temp">${Math.round(forecast.maxTemp)}° / ${Math.round(forecast.minTemp)}°</div>
                        <div class="forecast-desc">${weatherDescription}</div>
                    `;
                    
                    altContainer.appendChild(forecastItem);
                    console.log(`添加了预报项: ${dayName}, 天气: ${weatherDescription}`);
                } catch (error) {
                    console.error(`创建预报项 ${index} 时出错:`, error);
                }
            });
            return;
        }
        return;
    }
    
    // 清空现有预报
    forecastContainer.innerHTML = '';
    
    // 为每个预报日期创建一个预报项
    forecastData.forEach((forecast, index) => {
        try {
            const forecastDate = new Date(forecast.date);
            const dayName = weekdayNames[forecastDate.getDay()].replace('星期', '');
            
            const forecastItem = document.createElement('div');
            forecastItem.className = 'forecast-item';
            
            const iconClass = getWeatherIconClass(forecast.weatherCode);
            const weatherDescription = getWeatherDescription(forecast.weatherCode);
            
            forecastItem.innerHTML = `
                <div class="forecast-day">${dayName}</div>
                <div class="forecast-icon"><i class="wi ${iconClass}"></i></div>
                <div class="forecast-temp">${Math.round(forecast.maxTemp)}° / ${Math.round(forecast.minTemp)}°</div>
                <div class="forecast-desc">${weatherDescription}</div>
            `;
            
            forecastContainer.appendChild(forecastItem);
            console.log(`添加了预报项: ${dayName}, 天气: ${weatherDescription}`);
        } catch (error) {
            console.error(`创建预报项 ${index} 时出错:`, error);
        }
    });
}

// 根据OpenMeteo天气代码获取对应的天气图标CSS类
function getWeatherIconClass(code) {
    console.log(`正在查找天气代码 ${code} 的图标类`);
    
    // 使用Weather Icons库中的图标
    // 参考: https://erikflowers.github.io/weather-icons/
    
    // 将OpenMeteo代码映射到Weather Icons
    const weatherIconMap = {
        0: 'wi-day-sunny',           // 晴天
        1: 'wi-day-sunny-overcast',  // 晴间多云
        2: 'wi-day-cloudy',          // 多云
        3: 'wi-cloudy',              // 阴
        45: 'wi-fog',                // 雾
        48: 'wi-fog',                // 雾凇
        51: 'wi-sprinkle',           // 小毛毛雨
        53: 'wi-showers',            // 中毛毛雨
        55: 'wi-rain',               // 大毛毛雨
        56: 'wi-rain-mix',           // 小冻雨
        57: 'wi-rain-mix',           // 大冻雨
        61: 'wi-showers',            // 小雨
        63: 'wi-rain',               // 中雨
        65: 'wi-rain',               // 大雨
        66: 'wi-rain-mix',           // 小冻雨
        67: 'wi-rain-mix',           // 大冻雨
        71: 'wi-snow',               // 小雪
        73: 'wi-snow',               // 中雪
        75: 'wi-snow',               // 大雪
        77: 'wi-snow',               // 雪粒
        80: 'wi-showers',            // 小阵雨
        81: 'wi-rain',               // 中阵雨
        82: 'wi-rain',               // 大阵雨
        85: 'wi-snow',               // 小阵雪
        86: 'wi-snow',               // 大阵雪
        95: 'wi-thunderstorm',       // 雷暴
        96: 'wi-storm-showers',      // 雷暴伴有轻度冰雹
        99: 'wi-storm-showers'       // 雷暴伴有大冰雹
    };
    
    const iconClass = weatherIconMap[code] || 'wi-na';
    console.log(`天气代码 ${code} 映射到图标类: ${iconClass}`);
    return iconClass;
}

// 根据OpenMeteo天气代码获取天气描述
function getWeatherDescription(code) {
    const weatherDescMap = {
        0: '晴天',
        1: '晴间多云',
        2: '多云',
        3: '阴天',
        45: '雾',
        48: '雾凇',
        51: '小毛毛雨',
        53: '毛毛雨',
        55: '大毛毛雨',
        56: '小冻雨',
        57: '冻雨',
        61: '小雨',
        63: '中雨',
        65: '大雨',
        66: '小冻雨',
        67: '大冻雨',
        71: '小雪',
        73: '中雪',
        75: '大雪',
        77: '雪粒',
        80: '小阵雨',
        81: '中阵雨',
        82: '大阵雨',
        85: '小阵雪',
        86: '大阵雪',
        95: '雷暴',
        96: '雷暴伴有冰雹',
        99: '雷暴伴有大冰雹'
    };
    
    return weatherDescMap[code] || '未知天气';
}

// 添加字体加载检查
document.addEventListener('DOMContentLoaded', () => {
    // 检查weathericons字体是否加载
    console.log("检查天气图标字体加载状态...");
    
    const checkFontLoading = () => {
        const testElement = document.createElement('span');
        testElement.className = 'wi wi-day-sunny';
        testElement.style.visibility = 'hidden';
        document.body.appendChild(testElement);
        
        const fontLoaded = window.getComputedStyle(testElement).fontFamily.includes('weathericons');
        console.log("天气图标字体状态:", fontLoaded ? "已加载" : "未加载");
        
        if (!fontLoaded) {
            console.warn("警告: 天气图标字体可能未正确加载");
        }
        
        document.body.removeChild(testElement);
    };
    
    // 延迟检查以确保字体有时间加载
    setTimeout(checkFontLoading, 1000);
    
    // 检查关键元素的样式
    const weatherIcon = document.getElementById('weather-icon');
    const forecastContainer = document.getElementById('forecast-container');
    
    if (weatherIcon) {
        const styles = window.getComputedStyle(weatherIcon);
        console.log("主天气图标样式:", {
            fontSize: styles.fontSize,
            color: styles.color,
            visibility: styles.visibility,
            display: styles.display,
            fontFamily: styles.fontFamily
        });
    }
    
    if (forecastContainer) {
        const styles = window.getComputedStyle(forecastContainer);
        console.log("预报容器样式:", {
            display: styles.display,
            visibility: styles.visibility,
            height: styles.height,
            backgroundColor: styles.backgroundColor
        });
        
        // 检查预报项的样式
        const forecastItems = forecastContainer.getElementsByClassName('forecast-item');
        if (forecastItems.length > 0) {
            const itemStyles = window.getComputedStyle(forecastItems[0]);
            console.log("预报项样式:", {
                display: itemStyles.display,
                flexDirection: itemStyles.flexDirection,
                padding: itemStyles.padding,
                backgroundColor: itemStyles.backgroundColor
            });
        }
    }
});

// 获取月份（1-12）
function getMonth(date) {
    return date.getMonth() + 1; // JavaScript的getMonth()返回0-11，我们需要1-12
}