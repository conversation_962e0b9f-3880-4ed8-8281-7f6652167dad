// 生成图标并保存到文件
document.addEventListener('DOMContentLoaded', function() {
    // 在浏览器环境中无法直接写入文件系统
    // 这个脚本会在控制台输出使用方法，并提供下载图标的功能
    
    console.log("图标生成工具已加载，请在Chrome扩展页面中右键图标并保存");
    
    // 设置画布大小
    const canvas = document.getElementById('iconCanvas');
    const iconSizes = [16, 48, 128];
    
    // 生成并下载图标
    generateIcons();
    
    function generateIcons() {
        iconSizes.forEach(size => {
            // 设置画布大小
            canvas.width = size;
            canvas.height = size;
            
            // 获取上下文
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制背景
            drawBackground(ctx, size);
            
            // 绘制日期图标
            drawDateIcon(ctx, size);
            
            // 绘制天气图标
            drawWeatherIcon(ctx, size);
            
            // 将画布内容转换为图片
            const dataUrl = canvas.toDataURL('image/png');
            
            // 创建下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = dataUrl;
            downloadLink.download = `icon${size}.png`;
            
            // 添加到文档中
            document.body.appendChild(downloadLink);
            
            // 手动点击函数（需用户手动点击链接下载）
            const clickFunction = function() {
                downloadLink.click();
                document.body.removeChild(downloadLink);
                console.log(`图标 ${size}x${size} 已准备下载`);
            };
            
            // 输出下载指令到控制台
            console.log(`要下载 ${size}x${size} 图标，请执行 iconDownload${size}()`);
            window[`iconDownload${size}`] = clickFunction;
        });
    }
    
    // 绘制背景
    function drawBackground(ctx, size) {
        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, size, size);
        gradient.addColorStop(0, '#4a6cf7');  // 蓝色
        gradient.addColorStop(1, '#6a11cb');  // 紫色
        
        // 填充圆形背景
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
        ctx.fill();
    }
    
    // 绘制日期图标
    function drawDateIcon(ctx, size) {
        // 根据尺寸调整比例
        const scale = size / 128;
        
        // 绘制日期数字
        ctx.fillStyle = '#ffffff';
        ctx.font = `bold ${Math.floor(45 * scale)}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // 获取当前日期
        const day = new Date().getDate();
        ctx.fillText(day, size/2, size/2);
    }
    
    // 绘制天气图标
    function drawWeatherIcon(ctx, size) {
        // 如果图标太小，就不绘制天气图标
        if (size < 32) {
            return;
        }
        
        const scale = size / 128;
        
        // 绘制一个简单的太阳图标在右上角
        ctx.fillStyle = '#FFD700'; // 金色
        
        // 太阳位置和大小
        const sunRadius = Math.floor(12 * scale);
        const sunX = size - sunRadius - Math.floor(10 * scale);
        const sunY = sunRadius + Math.floor(10 * scale);
        
        // 绘制太阳圆形
        ctx.beginPath();
        ctx.arc(sunX, sunY, sunRadius, 0, Math.PI * 2);
        ctx.fill();
    }
}); 