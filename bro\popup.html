<!DOCTYPE html>
<html lang="zh-CN" style="height: 1200px;">
<head>
    <meta charset="UTF-8">
    <title>智能便利贴</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC&family=Caveat&display=swap');
        html {
            overflow: hidden;
        }
        body {
            /* 自适应容器大小 - 支持popup和侧边栏 */
            min-width: 300px;
            width: 100%;
            min-height: 600px; /* 恢复合理的最小高度 */
            height: 100vh; /* 恢复全屏高度 */
            padding: 15px;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', Arial, sans-serif;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            background-color: #fff9c4;
            color: #333;
            position: relative;
            overflow-y: auto;
            overflow-x: hidden;
            margin: 0;
        }
        h1 {
            font-family: 'Caveat', cursive;
            font-size: 20px; /* 缩小标题字体 */
            text-align: center;
            margin: 5px 0 10px 0; /* 缩小上下间距 */
        }
        button, select, input, textarea {
            margin: 3px 0; /* 缩小上下间距 */
            padding: 4px; /* 缩小内边距 */
            width: 100%;
            font-family: 'Caveat', cursive;
            font-size: 16px; /* 缩小字体 */
            border: 1px solid #d4d4d4;
            border-radius: 4px;
            background-color: #ffeb3b;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #fdd835;
        }
        #result {
            margin-top: 15px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            padding: 15px;
            /* 答案区域居中显示，占据主要视觉空间 */
            min-height: 250px;
            max-height: 55vh; /* 占据页面中间的主要空间 */
            height: auto;
            font-size: 14px;
            overflow-y: auto;
            white-space: pre-wrap;
            background-color: #fffde7;
            border-radius: 6px;
            flex-grow: 1; /* 让答案区域占据剩余空间 */
        }

        /* 为 #result 内的 HTML 元素添加样式 */
        #result h1, #result h2, #result h3 {
            margin: 15px 0 8px 0;
            color: #333;
            font-weight: bold;
            line-height: 1.3;
        }

        #result h1 { font-size: 18px; }
        #result h2 { font-size: 16px; }
        #result h3 { font-size: 14px; }

        #result p {
            margin: 8px 0;
            line-height: 1.5;
        }

        #result strong {
            font-weight: bold;
            color: #2c3e50;
        }

        #result em {
            font-style: italic;
            color: #7f8c8d;
        }

        #result code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 2px 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #e83e8c;
        }

        #result ul, #result ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        #result li {
            margin: 3px 0;
            line-height: 1.4;
        }

        #result ul li {
            list-style-type: disc;
        }

        #result ol li {
            list-style-type: decimal;
        }

        /* 为 #result 元素自定义滚动条样式 */
        #result::-webkit-scrollbar {
            width: 12px; /* 增加滚动条宽度 */
        }

        #result::-webkit-scrollbar-track {
            background: #f1f1f1; /* 滚动条轨道背景色 */
            border-radius: 6px;
        }

        /* 修改 #result 元素的滚动条样式 */
        #result::-webkit-scrollbar-thumb {
            background: #f1f1f1; /* 将滑块颜色改为与轨道背景色相同 */
            border-radius: 6px;
        }

        #result::-webkit-scrollbar-thumb:hover {
            background: #e1e1e1; /* 鼠标悬停时稍微加深一点，但仍保持不显眼 */
        }

        #result::-webkit-scrollbar-button {
            background-color: #ccc; /* 滚动条按钮（上下箭头）背景色 */
            height: 12px; /* 按钮高度 */
            width: 12px; /* 按钮宽度 */
            border-radius: 2px;
        }

        #result::-webkit-scrollbar-button:vertical:start:decrement {
            background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 12 12'><path fill='%23444' d='M6 4 L10 8 L2 8 Z'/></svg>");
        }

        #result::-webkit-scrollbar-button:vertical:end:increment {
            background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 12 12'><path fill='%23444' d='M6 8 L10 4 L2 4 Z'/></svg>");
        }

        #settingsPanel {
            display: none;
            background-color: #fff9c4;
            padding: 8px; /* 缩小内边距 */
            border-radius: 4px;
            border: 1px solid #d4d4d4;
            margin: 5px 0; /* 缩小上下边距 */
        }

        /* 添加以下内容来修改扩展按钮的背景色 */
        @media (prefers-color-scheme: light) {
            :root {
                --extension-icon-background: #fff9c4;
            }
        }
        @media (prefers-color-scheme: dark) {
            :root {
                --extension-icon-background: #fff9c4;
            }
        }
        
        textarea {
            height: 36px; /* 真正的2行高度 */
            margin: 5px 0; /* 缩小上下边距 */
            font-family: 'Noto Sans SC', 'Microsoft YaHei', Arial, sans-serif;
            font-size: 14px;
            background-color: #fffde7;
            border: 1px solid #bdb76b;
            resize: vertical;
            line-height: 1.2; /* 缩小行高 */
            padding: 4px 6px; /* 缩小内边距 */
            border-radius: 4px;
        }
        textarea::placeholder {
            color: #666; /* 更深的颜色以增加可见度 */
            opacity: 1; /* 确保占位符文本完全不透明 */
        }

        /* 输入区域容器样式 - 简化设计 */
        .input-section {
            margin: 5px 0; /* 缩小上下边距 */
            padding: 0; /* 移除内边距 */
        }
        #summarizeBtn {
            font-family: 'Caveat', cursive;
            font-size: 18px;
        }
        
        #clickCounter {
            text-align: center;
            font-size: 12px; /* 进一步减小字体大小 */
            background-color: #333;
            color: white;
            padding: 1px 4px; /* 减小内边距 */
            border-radius: 8px; /* 稍微减小圆角 */
            display: inline-block; /* 改为内联块级元素 */
            margin: 3px auto; /* 上下间距减小，左右自动居中 */
            font-family: 'Caveat', cursive; /* 使用手写体 */
            letter-spacing: 0.3px; /* 稍微减小字间距 */
            box-shadow: 0 1px 2px rgba(0,0,0,0.2); /* 添加轻微阴影 */
        }

        #clickCounter span {
            font-size: 14px; /* 数字稍大一些 */
            font-weight: bold; /* 数字加粗 */
        }

        .settings-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .settings-header h3 {
            font-family: 'Caveat', cursive, 'Noto Sans SC', 'Microsoft YaHei', Arial, sans-serif;
            font-size: 20px;
            line-height: 1.5;
            margin-bottom: 5px;
        }

        .settings-header p {
            font-size: 14px; /* 减小 "OK信心指数" 文字的大小 */
            margin: 0;
        }
        
        input[type="text"], select {
            font-family: 'Caveat', cursive;
            font-size: 16px;
            background-color: #fffde7;
            border: 1px solid #bdb76b;
        }

        input[type="text"]::placeholder {
            color: #888;
            opacity: 1;
        }
        
        :root {
            --primary-color: #fff9c4;
            --secondary-color: #ffeb3b;
            --tertiary-color: #fdd835;
            --text-color: #333;
        }

        body {
            background-color: var(--primary-color);
            color: var(--text-color);
        }

        button, select, input {
            background-color: var(--secondary-color);
        }

        button:hover {
            background-color: var(--tertiary-color);
        }

        .theme-switcher {
            position: absolute;
            top: 0;
            right: 0;
            width: 30px;
            height: 30px;
            cursor: pointer;
            background-color: var(--secondary-color);
            clip-path: polygon(100% 0, 0 0, 100% 100%);
            transition: background-color 0.3s;
        }
        
        #exportBtn {
            background-color: var(--secondary-color);
            color: var(--text-color);
            border: 1px solid #d4d4d4;
            border-radius: 4px;
            padding: 5px;
            font-family: 'Caveat', cursive;
            font-size: 18px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        #exportBtn:hover {
            background-color: var(--tertiary-color);
        }
        
        /* 添加以下样式来自定义滚动条 */
        ::-webkit-scrollbar {
            width: 4px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.5);
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.7);
        }

        .button-group {
            margin: 5px 0; /* 缩小按钮组的上下边距 */
        }

        /* 手机风格设置齿轮图标 */
        .gear-button {
            position: absolute !important;
            top: 12px;
            left: 12px;
            width: 20px !important;
            height: 20px !important;
            padding: 0 !important;
            margin: 0 !important;
            background: none !important;
            border: none !important;
            font-size: 16px;
            color: #888;
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gear-button:hover {
            color: #555;
            transform: rotate(45deg) scale(1.1);
        }

        /* 确保body有相对定位，为齿轮按钮提供定位基准 */
        body {
            position: relative;
        }


    </style>
</head>
<body>
    <h1>
        <span class="main-title" data-i18n="title"></span>
    </h1>
    <button id="settingsBtn" class="gear-button">⚙️</button>
    <div id="settingsPanel" style="display: none;">
        <div class="settings-header">
            <h3>
                <span id="dailyOkTitle" data-i18n="dailyOkTitle" style="font-family: 'Caveat', cursive;">Daily I'm ok 100+ times</span><br>
                <span id="becomeOkPerson" data-i18n="becomeOkPerson">你会变得很OK</span>
            </h3>
            <p><span id="okConfidenceIndex" data-i18n="okConfidenceIndex">OK信心指数</span> : <span id="clickCounter">0</span></p>
        </div>
        <div style="display: flex; align-items: center;">
            <input type="password" id="apiKey" placeholder="API Keys" style="flex: 1; font-family: 'Caveat', cursive; font-size: 18px;">
            <button id="toggleApiKeyVisibility" style="width: auto; padding: 5px; margin-left: 5px; font-size: 14px;">👁️</button>
        </div>
        <select id="apiPlatform">
            <option value="siliconflow">SiliconFlow</option>
            <option value="openrouter">OpenRouter</option>
            <option value="moonshot">Moonshot AI</option>
            <option value="ollama">Ollama</option>
        </select>
        <input type="text" id="baseUrl" placeholder="Base URL" readonly>
        <select id="modelSelect">
            <!-- 选项将通过 JavaScript 动态填充 -->
        </select>
        <input type="text" id="customModel" placeholder="自定义模型名称" style="display: none;">
        <label for="languageSelect">Language / 语言:</label>
        <select id="languageSelect">
            <option value="en">English</option>
            <option value="zh">中文(简体)</option>
            <option value="zh_tw">中文(繁體)</option>
        </select>
        <div class="setting-item">
            <label for="fontSizeSelect" id="fontSizeLabel">字体大小</label>
            <select id="fontSizeSelect">
                <option value="small">小</option>
                <option value="medium" selected>中</option>
                <option value="large">大</option>
            </select>
        </div>

        <div class="setting-item">
            <label for="statusDisplayToggle" id="statusDisplayLabel">显示状态栏</label>
            <select id="statusDisplayToggle">
                <option value="on">开启</option>
                <option value="off">关闭</option>
            </select>
        </div>
        <button id="clearNotesBtn" data-i18n="clearNotes">清空记录</button>
        <button id="saveSettings" data-i18n="saveSettings">保存设置</button>
    </div>
    <button id="summarizeBtn" data-i18n="summarize">I'm OK</button>

    <!-- 输入区域 -->
    <div class="input-section">
        <textarea id="customPrompt" rows="2" placeholder="在这里输入你的问题..."></textarea>
        <button id="askBtn" data-i18n="ask">Ask</button>
    </div>
    <!-- 状态显示区域 -->
    <div id="statusBar" style="display:none; background-color: #e8f5e8; border: 1px solid #4caf50; border-radius: 4px; padding: 8px; margin-bottom: 10px; font-size: 12px; color: #2e7d32;">
        <div id="statusInfo"></div>
    </div>

    <div id="result"></div>
    <div class="button-group">
        <button id="copyBtn" style="display:none;">✂ 复制</button>
        <button id="clearBtn" data-i18n="clear">Clear</button>
    </div>
    <button id="exportBtn" data-i18n="export">Export Notes</button>



    <div id="themeSwitcher" class="theme-switcher"></div>
    <script type="module" src="popup.js"></script>
</body>
</html>