<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64转本地图片测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }

        .input-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .api-config {
            margin-bottom: 15px;
        }

        .api-config label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .api-config input, .api-config select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 8px;
            font-size: 14px;
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .generate-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }

        .image-display {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .image-display h3 {
            color: #495057;
            margin-bottom: 15px;
        }

        .image-container {
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 10px;
            border: 2px dashed #dee2e6;
            margin-bottom: 15px;
        }

        .image-container img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .placeholder {
            color: #6c757d;
            font-style: italic;
        }

        .download-btn {
            padding: 10px 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }

        .download-btn:hover {
            background: #218838;
        }

        .download-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .info-panel {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }

        .info-panel h4 {
            color: #0056b3;
            margin-bottom: 10px;
        }

        .info-panel ul {
            color: #495057;
            padding-left: 20px;
        }

        .info-panel li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .control-panel,
            .result-section {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Base64转本地图片测试</h1>
        
        <div class="info-panel">
            <h4>💡 功能说明</h4>
            <ul>
                <li>使用Google Gemini API生成AI图片</li>
                <li>将返回的Base64数据转换为本地Blob URL</li>
                <li>实现永久保存到本地，无需云存储服务</li>
                <li>支持直接下载生成的图片</li>
            </ul>
        </div>

        <div class="control-panel">
            <div class="input-section">
                <h3>🔧 API配置</h3>
                <div class="api-config">
                    <label for="api-key">Gemini API密钥:</label>
                    <input type="password" id="api-key" placeholder="输入你的API密钥" value="AIzaSyDZRfmpBIMHeG_r9lx4ftaSWnP_p985JtI">
                </div>
                <div class="api-config">
                    <label for="flower-type">花卉类型:</label>
                    <select id="flower-type">
                        <option value="玫瑰">🌹 玫瑰</option>
                        <option value="牡丹">🌺 牡丹</option>
                        <option value="荷花">🪷 荷花</option>
                        <option value="樱花">🌸 樱花</option>
                        <option value="向日葵">🌻 向日葵</option>
                        <option value="郁金香">🌷 郁金香</option>
                    </select>
                </div>
                <div class="api-config">
                    <label for="art-style">艺术风格:</label>
                    <select id="art-style">
                        <option value="水彩画">水彩画风格</option>
                        <option value="油画">油画风格</option>
                        <option value="国画">中国国画风格</option>
                        <option value="素描">素描风格</option>
                        <option value="现代艺术">现代艺术风格</option>
                    </select>
                </div>
                <button class="generate-btn" id="generate-btn">🎨 生成AI图片</button>
            </div>

            <div class="input-section">
                <h3>📊 状态信息</h3>
                <div id="status" class="status">准备就绪</div>
                <div class="api-config">
                    <label>API限制:</label>
                    <div style="font-size: 14px; color: #6c757d;">
                        • 免费额度: 每天100张<br>
                        • 每分钟: 10次请求<br>
                        • 完全免费使用
                    </div>
                </div>
                <div class="api-config">
                    <label>技术优势:</label>
                    <div style="font-size: 14px; color: #28a745;">
                        ✅ 无需云存储费用<br>
                        ✅ 图片永久保存本地<br>
                        ✅ 支持离线访问<br>
                        ✅ 隐私安全保护
                    </div>
                </div>
            </div>
        </div>

        <div class="result-section">
            <div class="image-display">
                <h3>🖼️ 生成的图片</h3>
                <div class="image-container" id="image-container">
                    <div class="placeholder">点击生成按钮开始创作...</div>
                </div>
                <button class="download-btn" id="download-btn" disabled>📥 下载图片</button>
                <button class="download-btn" id="save-local-btn" disabled>💾 保存到本地</button>
            </div>

            <div class="image-display">
                <h3>📝 技术详情</h3>
                <div id="tech-info" style="text-align: left; padding: 20px; background: white; border-radius: 10px; min-height: 300px;">
                    <p><strong>等待生成图片...</strong></p>
                    <p>生成后将显示:</p>
                    <ul>
                        <li>Base64数据大小</li>
                        <li>图片格式信息</li>
                        <li>Blob URL详情</li>
                        <li>转换耗时统计</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
