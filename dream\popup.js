document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const dreamTimeInput = document.getElementById('dreamTime');
  const dreamLocationInput = document.getElementById('dreamLocation');
  const dreamDescriptionInput = document.getElementById('dreamDescription');
  const dreamExtraInfoInput = document.getElementById('dreamExtraInfo');
  const textModelSelect = document.getElementById('textModelSelect');
  const modelSelect = document.getElementById('modelSelect');
  const submitButton = document.getElementById('submitDream');
  const dreamAnalysisText = document.getElementById('dreamAnalysisText');
  const dreamImage = document.getElementById('dreamImage');
  const loadingDiv = document.getElementById('loading');
  const imageErrorText = document.getElementById('imageErrorText');
  
  // 设置相关元素
  const settingsBtn = document.getElementById('settingsBtn');
  const mainContent = document.getElementById('mainContent');
  const settingsContent = document.getElementById('settingsContent');
  const recordsContent = document.getElementById('recordsContent');
  const apiKeyInput = document.getElementById('apiKeyInput');
  const saveApiKeyBtn = document.getElementById('saveApiKey');
  const fontSizeSelect = document.getElementById('fontSizeSelect');
  const viewRecordsBtn = document.getElementById('viewRecordsBtn');
  const backToMainBtn = document.getElementById('backToMainBtn');
  const backFromRecordsBtn = document.getElementById('backFromRecordsBtn');
  const dreamRecordsList = document.getElementById('dreamRecordsList');

  // 硅基流动 API 密钥
  let SILICONFLOW_API_KEY = 'sk-uakaufumckwvyevxbqiwvlasfluopchztcambehuahcnqoyr';
  
  // 请求配置
  const MAX_RETRIES = 3; // 最大重试次数
  const RETRY_DELAY = 1000; // 重试延迟（毫秒）

  // 添加文本风格选择事件
  const textStyleSelect = document.getElementById('textStyleSelect');
  if (textStyleSelect) {
    textStyleSelect.addEventListener('change', function() {
      applyTextStyle(textStyleSelect.value);
    });
    
    // 初始设置文本风格
    applyTextStyle(textStyleSelect.value);
  }

  // 初始化
  initializeApp();

  function initializeApp() {
    // 设置当前时间为默认值
    setDefaultTime();
    
    // 加载设置
    loadSettings();
    
    // 添加事件监听器
    addEventListeners();
  }

  function setDefaultTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    dreamTimeInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  function loadSettings() {
    // 加载API密钥
    chrome.storage.local.get(['apiKey'], function(result) {
      if (result.apiKey) {
        SILICONFLOW_API_KEY = result.apiKey;
        apiKeyInput.value = result.apiKey;
      }
    });
    
    // 加载字体大小设置
    chrome.storage.local.get(['fontSize'], function(result) {
      if (result.fontSize) {
        fontSizeSelect.value = result.fontSize;
        applyFontSize(result.fontSize);
      } else {
        applyFontSize('medium');
      }
    });
  }

  function addEventListeners() {
    // 提交按钮点击事件
    submitButton.addEventListener('click', handleSubmit);
    
    // 设置按钮点击事件
    settingsBtn.addEventListener('click', function() {
      mainContent.style.display = 'none';
      settingsContent.style.display = 'block';
      recordsContent.style.display = 'none';
    });
    
    // 保存API密钥按钮点击事件
    saveApiKeyBtn.addEventListener('click', function() {
      const newApiKey = apiKeyInput.value.trim();
      if (newApiKey) {
        SILICONFLOW_API_KEY = newApiKey;
        chrome.storage.local.set({ apiKey: newApiKey }, function() {
          alert('API Key已保存');
        });
      } else {
        alert('请输入有效的API Key');
      }
    });
    
    // 字体大小选择事件
    fontSizeSelect.addEventListener('change', function() {
      const fontSize = fontSizeSelect.value;
      applyFontSize(fontSize);
      chrome.storage.local.set({ fontSize: fontSize });
    });
    
    // 查看记录按钮点击事件
    viewRecordsBtn.addEventListener('click', function() {
      mainContent.style.display = 'none';
      settingsContent.style.display = 'none';
      recordsContent.style.display = 'block';
      loadDreamRecords();
    });
    
    // 返回按钮点击事件
    backToMainBtn.addEventListener('click', function() {
      mainContent.style.display = 'block';
      settingsContent.style.display = 'none';
      recordsContent.style.display = 'none';
    });
    
    // 从记录返回按钮点击事件
    backFromRecordsBtn.addEventListener('click', function() {
      mainContent.style.display = 'none';
      settingsContent.style.display = 'block';
      recordsContent.style.display = 'none';
    });
  }

  function applyFontSize(size) {
    document.body.classList.remove('font-small', 'font-medium', 'font-large');
    document.body.classList.add(`font-${size}`);
  }

  function loadDreamRecords() {
    chrome.storage.local.get(['dreamRecords'], function(result) {
      const records = result.dreamRecords || [];
      
      if (records.length === 0) {
        dreamRecordsList.innerHTML = '<p>暂无梦境记录</p>';
        return;
      }
      
      // 按时间倒序排列
      records.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      
      // 构建记录列表HTML
      let recordsHtml = '';
      records.forEach(record => {
        const date = new Date(record.time).toLocaleString();
        // 确定文字样式类
        const textStyleClass = record.textStyle === 'handwritten' ? 'handwritten-text' : '';
        // 根据文字风格设置字体
        const descriptionStyle = record.textStyle === 'handwritten' ? 
          'font-family: \'Ma Shan Zheng\', cursive; font-size: 18px;' : 
          'font-family: \'ZCOOL XiaoWei\', serif; font-size: 16px;';
        
        // 生成唯一的图片ID
        const imgId = `dream-img-${record.id || Date.now() + Math.random().toString(36).substring(2, 8)}`;
        
        recordsHtml += `
          <div class="dream-record-item">
            <h3>${formatDate(record.time)}</h3>
            <p><strong>地点:</strong> ${record.location}</p>
            <p class="${textStyleClass}" style="${descriptionStyle}"><strong>描述:</strong> ${record.description}</p>
            ${record.extraInfo ? `<p><strong>附加信息:</strong> ${record.extraInfo}</p>` : ''}
            <div class="${textStyleClass}" style="${descriptionStyle}"><strong>分析:</strong><br>${parseMarkdown(record.analysis)}</div>
            ${record.imageUrl ?
              `<div class="dream-image-container">
                <img id="${imgId}" src="${record.imageUrl}" alt="梦境图像"
                     onclick="openImageModal(this.src)"
                     onerror="this.onerror=null; this.src=''; this.alt='图片加载失败'; this.classList.add('image-error');">
                <div class="image-loading-error" style="display:none;">图片加载失败，可能已过期或无法访问</div>
               </div>`
              : ''}
            <p><small>使用模型: ${getModelDisplayName(record.imageModel)}</small></p>
            ${record.textStyle ? `<p><small>文字风格: ${record.textStyle === 'handwritten' ? '手写风格' : '普通文字'}</small></p>` : ''}
          </div>
        `;
      });
      
      // 添加清空记录按钮
      recordsHtml += `
        <div style="text-align: center; margin-top: 20px;">
          <button id="clearRecordsBtn" class="secondary-button" style="background-color: #f44336; color: white;">
            <i class="fas fa-trash"></i> 清空所有记录
          </button>
        </div>
      `;
      
      dreamRecordsList.innerHTML = recordsHtml;
      
      // 添加图片加载错误处理
      records.forEach(record => {
        if (record.imageUrl) {
          const imgId = `dream-img-${record.id || Date.now() + Math.random().toString(36).substring(2, 8)}`;
          const imgElement = document.getElementById(imgId);
          if (imgElement) {
            imgElement.addEventListener('error', function() {
              this.style.display = 'none';
              const errorDiv = this.parentNode.querySelector('.image-loading-error');
              if (errorDiv) {
                errorDiv.style.display = 'block';
              }
            });
          }
        }
      });
      
      // 添加清空记录按钮的点击事件
      document.getElementById('clearRecordsBtn').addEventListener('click', function() {
        if (confirm('确定要清空所有梦境记录吗？此操作不可撤销。')) {
          chrome.storage.local.set({ dreamRecords: [] }, function() {
            alert('所有梦境记录已清空');
            loadDreamRecords(); // 重新加载记录列表
          });
        }
      });
    });
  }

  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  function getModelDisplayName(modelId) {
    switch (modelId) {
      case 'black-forest-labs/FLUX.1-schnell':
        return 'FLUX.1-schnell (快速)';
      case 'black-forest-labs/FLUX.1-dev':
        return 'FLUX.1-dev (高质量)';
      case 'stabilityai/stable-diffusion-3-5-large':
        return 'Stable Diffusion 3.5';
      default:
        return modelId;
    }
  }

  // 提交按钮点击事件处理
  async function handleSubmit() {
    // 获取用户输入
    const dreamTime = dreamTimeInput.value;
    const dreamLocation = dreamLocationInput.value;
    const dreamDescription = dreamDescriptionInput.value;
    const dreamExtraInfo = dreamExtraInfoInput.value;
    const selectedTextModel = textModelSelect.value;
    const selectedModel = modelSelect.value;
    const selectedTextStyle = document.getElementById('textStyleSelect').value;

    // 验证输入
    if (!dreamTime || !dreamLocation || !dreamDescription) {
      alert('请填写时间、地点和梦境描述！');
      return;
    }

    // 显示加载状态
    loadingDiv.style.display = 'block';
    dreamAnalysisText.textContent = '正在分析...';
    dreamImage.style.display = 'none';
    imageErrorText.style.display = 'none';
    submitButton.disabled = true;

    try {
      // 1. 调用硅基流动 API 进行梦境分析
      dreamAnalysisText.textContent = '正在分析梦境...';
      const dreamAnalysis = await fetchWithRetry(() => analyzeDreamWithSiliconFlow(dreamDescription, dreamLocation, dreamTime, selectedTextModel));
      // 处理markdown格式
      console.log("原始分析结果:", dreamAnalysis);
      dreamAnalysisText.innerHTML = parseMarkdown(dreamAnalysis);

      // 2. 调用硅基流动 API 生成图像
      imageErrorText.textContent = '正在生成图像...';
      imageErrorText.style.display = 'block';
      imageErrorText.style.color = '#666';
      const imageUrl = await fetchWithRetry(() => generateDreamImageWithSiliconFlow(dreamDescription, dreamLocation, dreamExtraInfo, selectedModel, selectedTextModel));
      
      // 显示生成的图像
      dreamImage.onload = function() {
        // 图像加载成功
        dreamImage.style.display = 'block';
        imageErrorText.style.display = 'none';
        // 显示导出按钮
        document.getElementById('exportDreamBtn').style.display = 'block';
      };
      
      dreamImage.onerror = function() {
        // 图像加载失败
        console.error('图像加载失败:', imageUrl);
        imageErrorText.textContent = '图像加载失败，请检查网络连接或刷新页面重试';
        imageErrorText.style.color = 'red';
        imageErrorText.style.display = 'block';
        dreamImage.style.display = 'none';
      };
      
      // 设置图像源
      dreamImage.src = imageUrl;
      
      // 保存到本地存储
      saveDreamRecord(dreamTime, dreamLocation, dreamDescription, dreamExtraInfo, dreamAnalysis, imageUrl, selectedModel, selectedTextModel, selectedTextStyle);
    } 
    catch (error) {
      console.error('Error:', error);
      dreamAnalysisText.textContent = error.message.includes('解梦分析') ? 
        error.message : '分析过程中出现错误，请稍后再试。';
      
      imageErrorText.textContent = error.message.includes('图像生成') ? 
        error.message : '图像生成失败，请稍后重试。可能的原因：网络问题或API服务暂时不可用。';
      imageErrorText.style.display = 'block';
      imageErrorText.style.color = 'red';
    } 
    finally {
      // 隐藏加载状态
      loadingDiv.style.display = 'none';
      submitButton.disabled = false;
    }
  }

  /**
   * 带重试机制的网络请求函数
   * @param {Function} fetchFunction - 执行网络请求的函数
   * @param {number} maxRetries - 最大重试次数
   * @returns {Promise<any>} - 请求结果
   */
  async function fetchWithRetry(fetchFunction, maxRetries = MAX_RETRIES) {
    let lastError;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await fetchFunction();
      } catch (error) {
        console.log(`尝试 ${attempt + 1}/${maxRetries} 失败:`, error);
        lastError = error;
        
        // 如果不是最后一次尝试，则等待后重试
        if (attempt < maxRetries - 1) {
          // 指数退避策略
          const delay = RETRY_DELAY * Math.pow(2, attempt);
          console.log(`等待 ${delay}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 使用硅基流动 API 进行梦境分析
   * @param {string} description - 梦境描述
   * @param {string} location - 梦境发生地点
   * @param {string} time - 梦境发生时间
   * @param {string} textModel - 使用的文本模型
   * @returns {Promise<string>} - 解梦分析结果
   */
  async function analyzeDreamWithSiliconFlow(description, location, time, textModel) {
    const url = "https://api.siliconflow.cn/v1/chat/completions";
    const payload = {
      "model": textModel,
      "messages": [
        {
          "role": "system",
          "content": "你是一位专业的解梦分析师，擅长从心理学、象征意义和文化背景等多角度分析梦境。请提供深入、有见地的解析，同时保持尊重和敏感。输出格式要求：使用标准的markdown格式，用## 作为二级标题，用**粗体**强调关键词，保持内容清晰易读。"
        },
        {
          "role": "user",
          "content": `请分析以下梦境：
**时间：** ${time}
**地点：** ${location}
**梦境描述：** ${description}

请按以下格式进行分析：

## 核心象征和意义
分析梦境中的主要元素和象征意义

## 潜意识反映
探讨可能反映的内心想法或情感状态

## 现实联系
与当前生活状况的可能关联

请用简洁的语言进行分析，控制在300-400字，严格使用markdown格式输出。`
        }
      ],
      "stream": false,
      "max_tokens": 1024,
      "temperature": 0.7,
      "top_p": 0.7
    };

    try {
      console.log("开始发送解梦分析请求...");
      console.log("使用文本模型:", textModel);
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SILICONFLOW_API_KEY}`
        },
        body: JSON.stringify(payload)
      });
  
      if (!response.ok) {
        const errorText = await response.text().catch(() => "无法获取错误详情");
        console.error("API响应错误:", response.status, errorText);
        throw new Error(`解梦分析API请求失败: ${response.status} ${response.statusText}`);
      }
  
      const data = await response.json();
      console.log("解梦分析API响应:", data);
      
      if (data.choices && data.choices.length > 0 && data.choices[0].message) {
        return data.choices[0].message.content.trim();
      } else {
        console.error("API响应结构异常:", data);
        throw new Error('解梦分析API返回了意外的响应结构');
      }
    } catch (error) {
      console.error("解梦分析请求错误:", error);
      if (error.message.includes('Failed to fetch')) {
        throw new Error('解梦分析请求失败，请检查您的网络连接或API服务是否可用');
      }
      throw error;
    }
  }

  /**
   * 使用硅基流动 API 生成梦境图像
   * @param {string} description - 梦境描述
   * @param {string} location - 梦境发生地点
   * @param {string} extraInfo - 附加信息，如风格描述
   * @param {string} model - 选择的图像生成模型
   * @param {string} textModel - 选择的文本模型
   * @returns {Promise<string>} - 生成的图像URL
   */
  async function generateDreamImageWithSiliconFlow(description, location, extraInfo, model, textModel) {
    console.log("原始梦境描述:", description);
    console.log("梦境地点:", location);
    console.log("额外信息:", extraInfo);
    console.log("选择的图像模型:", model);
    console.log("选择的文本模型:", textModel);
    
    // 组合完整的梦境描述
    let fullDescription = description;
    if (location && location.trim() !== '') {
      fullDescription = `地点：${location}。${fullDescription}`;
    }
    
    // 对所有模型都将描述翻译为英文
    const translatedDescription = await translateToEnglish(fullDescription, textModel);
    console.log("翻译后的描述:", translatedDescription);
    
    // 构建提示词
    let prompt = translatedDescription;
    
    // 添加风格信息
    if (extraInfo && extraInfo.trim() !== '') {
      // 预设风格的直接英文映射，避免翻译误差
      const styleMapping = {
        '油画风格': 'oil painting style',
        '水彩风格': 'watercolor painting style',
        '素描风格': 'pencil sketch style',
        '浮世绘风格': 'ukiyo-e style, Japanese woodblock print style'
      };

      const englishStyle = styleMapping[extraInfo] || await translateToEnglish(extraInfo, textModel);
      prompt += `, ${englishStyle}`;
    }
    
    // 根据不同模型添加特定的质量描述词
    if (model === "stabilityai/stable-diffusion-3-5-large") {
      // SD 3.5 的质量描述词
      prompt += `, dreamlike atmosphere, soft lighting, ethereal, mystical, gentle details, artistic quality`;
    } else if (model === "black-forest-labs/FLUX.1-dev") {
      // FLUX.1-dev 的质量描述词
      prompt += `, dreamlike scene, surreal, detailed, high quality rendering, 8k resolution`;
    } else {
      // FLUX.1-schnell 的质量描述词 (更简洁，因为这是快速模型)
      prompt += `, dreamlike, detailed, high quality`;
    }
    
    console.log("最终生成的提示词:", prompt);
    
    const url = "https://api.siliconflow.cn/v1/images/generations";
    
    // 根据不同模型设置不同的参数
    let payload = {
      model: model,
      prompt: prompt,
      image_size: "1024x1024", // 默认使用 1024x1024 尺寸
      batch_size: 1,
      seed: Math.floor(Math.random() * 4294967295) // 随机种子
    };
    
    // 为 Stable Diffusion 模型添加特定参数
    if (model === "stabilityai/stable-diffusion-3-5-large") {
      payload.negative_prompt = "blurry, distorted, low quality, incomplete, cropped, text, watermark, oversaturated, over-detailed, artificial textures, harsh lighting, too sharp";
      payload.num_inference_steps = 28; // 适当减少步数
      payload.guidance_scale = 5.5; // 降低 guidance_scale 以获得更自然的效果
    }
    
    // 为 FLUX.1-dev 模型添加特定参数
    if (model === "black-forest-labs/FLUX.1-dev") {
      payload.num_inference_steps = 25; // 增加 FLUX.1-dev 步数以提高质量
    }
    
    // FLUX.1-schnell 不需要额外参数

    console.log("生成图像请求参数:", payload);

    try {
      console.log("开始发送图像生成请求...");
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SILICONFLOW_API_KEY}`
        },
        body: JSON.stringify(payload)
      });
  
      if (!response.ok) {
        const errorText = await response.text().catch(() => "无法获取错误详情");
        console.error("API响应错误:", response.status, errorText);
        throw new Error(`图像生成失败! 状态码: ${response.status} ${response.statusText}`);
      }
  
      const data = await response.json();
      console.log("图像生成API响应:", data);
      
      if (data.data && data.data.length > 0 && data.data[0].url) {
        return data.data[0].url;
      } else {
        console.error("API响应结构异常:", data);
        throw new Error('图像生成API返回了意外的响应结构');
      }
    } catch (error) {
      console.error("图像生成请求错误:", error);
      if (error.message.includes('Failed to fetch')) {
        throw new Error('图像生成请求失败，请检查您的网络连接或API服务是否可用');
      }
      throw error;
    }
  }
  
  /**
   * 将中文文本翻译为英文
   * @param {string} text - 要翻译的中文文本
   * @param {string} textModel - 使用的文本模型
   * @returns {Promise<string>} - 翻译后的英文文本
   */
  async function translateToEnglish(text, textModel) {
    const url = "https://api.siliconflow.cn/v1/chat/completions";
    const payload = {
      "model": textModel,
      "messages": [
        {
          "role": "system",
          "content": "You are a translator specialized in translating dream descriptions from Chinese to English for image generation purposes. Your task is to translate the Chinese text into English that is optimized for image generation models. Focus on visual elements, emotions, and atmosphere. Maintain the dreamlike quality and surreal elements. Only provide the direct translation without any explanations or additional text. Do not summarize or shorten the content - translate the full description accurately."
        },
        {
          "role": "user",
          "content": `Translate the following Chinese dream description to English, optimizing it for image generation. Preserve all visual details and emotional elements:\n\n${text}`
        }
      ],
      "stream": false,
      "max_tokens": 1024,
      "temperature": 0.3,
      "top_p": 0.9
    };

    try {
      console.log("开始翻译文本...");
      console.log("使用文本模型:", textModel);
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SILICONFLOW_API_KEY}`
        },
        body: JSON.stringify(payload)
      });
  
      if (!response.ok) {
        const errorText = await response.text().catch(() => "无法获取错误详情");
        console.error("翻译API响应错误:", response.status, errorText);
        return simpleTranslate(text);
      }
  
      const data = await response.json();
      
      if (data.choices && data.choices.length > 0 && data.choices[0].message) {
        const translation = data.choices[0].message.content.trim();
        console.log("翻译结果:", translation);
        return translation;
      } else {
        console.error("翻译API返回了意外的响应结构，使用简单翻译");
        return simpleTranslate(text);
      }
    } catch (error) {
      console.error("翻译出错:", error);
      return simpleTranslate(text);
    }
  }
  
  /**
   * 简单的翻译方法，作为备用
   * @param {string} text - 要翻译的文本
   * @returns {string} - 简单处理后的文本
   */
  function simpleTranslate(text) {
    // 这是一个备用方案，在API调用失败时使用
    // 提取文本中的关键词和短语
    const keywords = text.split(/[，。！？,.!?]/)
      .filter(item => item.trim().length > 0)
      .slice(0, 5)  // 取前5个短语
      .join(', ');
      
    return `A dreamlike scene depicting: ${keywords}, surreal, ethereal, detailed, mystical dream`;
  }

  /**
   * 检测文本是否包含中文
   * @param {string} text - 要检查的文本
   * @returns {boolean} - 是否包含中文
   */
  function isChineseText(text) {
    return /[\u4e00-\u9fa5]/.test(text);
  }

  /**
   * 保存梦境记录到本地存储
   */
  function saveDreamRecord(time, location, description, extraInfo, analysis, imageUrl, imageModel, textModel, textStyle) {
    // 获取现有记录
    chrome.storage.local.get(['dreamRecords'], function(result) {
      let records = result.dreamRecords || [];
      
      // 添加新记录
      records.push({
        id: Date.now(), // 唯一ID
        time: time,
        location: location,
        description: description,
        extraInfo: extraInfo,
        analysis: analysis,
        imageUrl: imageUrl,
        imageModel: imageModel,
        textModel: textModel,
        textStyle: textStyle || 'normal', // 默认为普通文字风格
        createdAt: new Date().toISOString()
      });
      
      // 保存回本地存储
      chrome.storage.local.set({ dreamRecords: records }, function() {
        console.log('梦境记录已保存');
      });
    });
  }

  // 当生成梦境图片成功后，显示导出按钮
  function showExportButton() {
    document.getElementById('exportDreamBtn').style.display = 'flex';
    
    // 添加冲洗底片按钮
    const exportButtonsContainer = document.getElementById('exportDreamBtn').parentElement;
    
    // 检查是否已经添加了冲洗底片按钮
    if (!document.getElementById('developFilmBtn')) {
      const developFilmBtn = document.createElement('button');
      developFilmBtn.id = 'developFilmBtn';
      developFilmBtn.className = 'secondary-button';
      developFilmBtn.innerHTML = '<i class="fas fa-film"></i> 冲洗底片';
      developFilmBtn.style.marginLeft = '10px';
      developFilmBtn.style.display = 'inline-flex';
      developFilmBtn.style.alignItems = 'center';
      developFilmBtn.style.justifyContent = 'center';
      
      // 添加点击事件
      developFilmBtn.addEventListener('click', function() {
        // 显示模态框
        document.getElementById('exportModal').classList.add('active');
      });
      
      // 将按钮添加到容器中
      exportButtonsContainer.appendChild(developFilmBtn);
    }
  }

  // 修改现有的图片加载成功回调，添加显示导出按钮
  const originalImageOnload = dreamImage.onload;
  dreamImage.onload = function() {
    if (originalImageOnload) originalImageOnload();
    dreamImage.style.display = 'block';
    showExportButton();
  };

  // 应用文本风格
  function applyTextStyle(style) {
    if (style === 'handwritten') {
      document.body.classList.add('use-handwritten');
    } else {
      document.body.classList.remove('use-handwritten');
    }
  }

  // Markdown解析函数
  function parseMarkdown(text) {
    try {
      if (typeof marked !== 'undefined' && marked.parse) {
        return marked.parse(text);
      } else {
        // 简单的markdown替换
        let formattedText = text
          .replace(/## (.*?)$/gm, '<h3>$1</h3>')
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\n\n/g, '</p><p>')
          .replace(/\n/g, '<br>');
        return '<p>' + formattedText + '</p>';
      }
    } catch(e) {
      console.error("Markdown解析错误:", e);
      return text;
    }
  }

  // 图片模态框功能
  window.openImageModal = function(imageSrc) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = imageSrc;
    img.style.cssText = `
      max-width: 90%;
      max-height: 90%;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    `;

    modal.appendChild(img);
    document.body.appendChild(modal);

    // 点击关闭
    modal.addEventListener('click', function() {
      document.body.removeChild(modal);
    });

    // ESC键关闭
    const closeOnEsc = function(e) {
      if (e.key === 'Escape') {
        document.body.removeChild(modal);
        document.removeEventListener('keydown', closeOnEsc);
      }
    };
    document.addEventListener('keydown', closeOnEsc);
  };
});