<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .game-container {
            display: flex;
            gap: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .game-board {
            border: 3px solid #1976d2;
            border-radius: 10px;
            background: #f5f5f5;
        }
        
        .info-panel {
            width: 200px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #1976d2;
            text-align: center;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
            font-size: 16px;
        }
        
        .score {
            font-size: 24px;
            font-weight: bold;
            color: #0d47a1;
        }
        
        .level {
            font-size: 18px;
            color: #1976d2;
        }
        
        .next-piece {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            border: 1px solid #1976d2;
            background: white;
        }
        
        .controls {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #1976d2;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .controls h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(25, 118, 210, 0.95);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            display: none;
        }
        
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        
        button:hover {
            background: #0d47a1;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <canvas id="gameBoard" class="game-board" width="300" height="600"></canvas>
        <div class="info-panel">
            <div class="info-box">
                <h3>得分</h3>
                <div class="score" id="score">0</div>
            </div>
            <div class="info-box">
                <h3>等级</h3>
                <div class="level" id="level">1</div>
            </div>
            <div class="info-box">
                <h3>行数</h3>
                <div class="level" id="lines">0</div>
            </div>
            <div class="info-box">
                <h3>手柄状态</h3>
                <div class="level" id="gamepadStatus" style="font-size: 12px; color: #666;">未连接</div>
            </div>
            <div class="info-box">
                <h3>下一个</h3>
                <canvas class="next-piece" id="nextPiece" width="80" height="80"></canvas>
            </div>
            <div class="controls">
                <h3>控制说明</h3>
                <div style="text-align: left; font-size: 11px;">
                    <strong>← →</strong> 左右移动<br>
                    <strong>↓</strong> 快速下降<br>
                    <strong>↑</strong> 旋转方块<br>
                    <strong>Ctrl+空格</strong> 硬降<br>
                    <strong>空格</strong> 暂停游戏<br>
                    <strong>R</strong> 重新开始<br><br>
                    <strong>手柄控制：</strong><br>
                    • 方向键：移动/旋转<br>
                    • A键：旋转<br>
                    • B键：硬降<br>
                    • Start：暂停<br>
                    • Select：重新开始<br><br>
                    <strong>得分规则：</strong><br>
                    • 单行: 100×等级<br>
                    • 双行: 300×等级<br>
                    • 三行: 500×等级<br>
                    • 四行: 800×等级<br>
                    • 快速下降: +1分<br>
                    • 硬降: +2分/格
                </div>
            </div>
        </div>
    </div>
    
    <div class="game-over" id="gameOver">
        <h2>游戏结束</h2>
        <p>最终得分: <span id="finalScore">0</span></p>
        <button onclick="restartGame()">重新开始</button>
    </div>

    <script>
        // 游戏配置
        const BOARD_WIDTH = 10;
        const BOARD_HEIGHT = 20;
        const BLOCK_SIZE = 30;
        
        // 获取画布和上下文
        const canvas = document.getElementById('gameBoard');
        const ctx = canvas.getContext('2d');
        const nextCanvas = document.getElementById('nextPiece');
        const nextCtx = nextCanvas.getContext('2d');
        
        // 游戏状态
        let board = [];
        let currentPiece = null;
        let nextPiece = null;
        let score = 0;
        let level = 1;
        let lines = 0;
        let gameRunning = true;
        let isPaused = false;
        let dropTime = 0;
        let dropInterval = 1000;
        let lastTime = 0;
        let animationId = null;

        // 游戏手柄状态
        let gamepadConnected = false;
        let gamepadIndex = -1;
        let gamepadButtonStates = {};
        let gamepadLastUpdate = 0;
        
        // 方块形状定义
        const PIECES = {
            I: {
                shape: [
                    [1, 1, 1, 1]
                ],
                color: '#00bcd4'
            },
            O: {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffeb3b'
            },
            T: {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1]
                ],
                color: '#9c27b0'
            },
            S: {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0]
                ],
                color: '#4caf50'
            },
            Z: {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1]
                ],
                color: '#f44336'
            },
            J: {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1]
                ],
                color: '#2196f3'
            },
            L: {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1]
                ],
                color: '#ff9800'
            }
        };
        
        // 初始化游戏板
        function initBoard() {
            board = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));
        }
        
        // 创建新方块
        function createPiece() {
            const pieces = Object.keys(PIECES);
            const randomPiece = pieces[Math.floor(Math.random() * pieces.length)];
            const piece = PIECES[randomPiece];
            
            return {
                shape: piece.shape,
                color: piece.color,
                x: Math.floor(BOARD_WIDTH / 2) - Math.floor(piece.shape[0].length / 2),
                y: 0
            };
        }
        
        // 绘制方块
        function drawBlock(ctx, x, y, color) {
            const blockX = x * BLOCK_SIZE;
            const blockY = y * BLOCK_SIZE;

            // 主要颜色
            ctx.fillStyle = color;
            ctx.fillRect(blockX, blockY, BLOCK_SIZE, BLOCK_SIZE);

            // 高光效果
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.fillRect(blockX + 1, blockY + 1, BLOCK_SIZE - 2, 3);
            ctx.fillRect(blockX + 1, blockY + 1, 3, BLOCK_SIZE - 2);

            // 阴影效果
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.fillRect(blockX + BLOCK_SIZE - 3, blockY + 3, 3, BLOCK_SIZE - 3);
            ctx.fillRect(blockX + 3, blockY + BLOCK_SIZE - 3, BLOCK_SIZE - 3, 3);

            // 边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.strokeRect(blockX, blockY, BLOCK_SIZE, BLOCK_SIZE);
        }
        
        // 绘制游戏板
        function drawBoard() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制已放置的方块
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    if (board[y][x]) {
                        drawBlock(ctx, x, y, board[y][x]);
                    }
                }
            }

            // 绘制幽灵方块（预览位置）
            if (currentPiece) {
                drawGhostPiece();
            }

            // 绘制当前方块
            if (currentPiece) {
                drawPiece(ctx, currentPiece);
            }
        }

        // 绘制幽灵方块
        function drawGhostPiece() {
            const ghostPiece = {
                ...currentPiece,
                y: getGhostY()
            };

            ctx.save();
            ctx.globalAlpha = 0.3;
            for (let y = 0; y < ghostPiece.shape.length; y++) {
                for (let x = 0; x < ghostPiece.shape[y].length; x++) {
                    if (ghostPiece.shape[y][x]) {
                        ctx.fillStyle = ghostPiece.color;
                        ctx.fillRect(
                            (ghostPiece.x + x) * BLOCK_SIZE,
                            (ghostPiece.y + y) * BLOCK_SIZE,
                            BLOCK_SIZE,
                            BLOCK_SIZE
                        );
                        ctx.strokeStyle = '#666';
                        ctx.strokeRect(
                            (ghostPiece.x + x) * BLOCK_SIZE,
                            (ghostPiece.y + y) * BLOCK_SIZE,
                            BLOCK_SIZE,
                            BLOCK_SIZE
                        );
                    }
                }
            }
            ctx.restore();
        }

        // 获取幽灵方块的Y位置
        function getGhostY() {
            let ghostY = currentPiece.y;
            while (!checkCollision(currentPiece, 0, ghostY - currentPiece.y + 1)) {
                ghostY++;
            }
            return ghostY;
        }

        // 硬降功能
        function hardDrop() {
            const ghostY = getGhostY();
            const dropDistance = ghostY - currentPiece.y;
            currentPiece.y = ghostY;
            score += dropDistance * 2; // 硬降奖励更多分数
            updateDisplay();

            // 立即放置方块
            placePiece();
            clearLines();
            currentPiece = nextPiece;
            nextPiece = createPiece();

            if (checkCollision(currentPiece)) {
                gameOver();
            }
        }
        
        // 绘制方块
        function drawPiece(context, piece) {
            for (let y = 0; y < piece.shape.length; y++) {
                for (let x = 0; x < piece.shape[y].length; x++) {
                    if (piece.shape[y][x]) {
                        drawBlock(context, piece.x + x, piece.y + y, piece.color);
                    }
                }
            }
        }
        
        // 检查碰撞
        function checkCollision(piece, dx = 0, dy = 0, newShape = null) {
            const shape = newShape || piece.shape;
            const newX = piece.x + dx;
            const newY = piece.y + dy;
            
            for (let y = 0; y < shape.length; y++) {
                for (let x = 0; x < shape[y].length; x++) {
                    if (shape[y][x]) {
                        const boardX = newX + x;
                        const boardY = newY + y;
                        
                        if (boardX < 0 || boardX >= BOARD_WIDTH || 
                            boardY >= BOARD_HEIGHT || 
                            (boardY >= 0 && board[boardY][boardX])) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        
        // 放置方块到游戏板
        function placePiece() {
            for (let y = 0; y < currentPiece.shape.length; y++) {
                for (let x = 0; x < currentPiece.shape[y].length; x++) {
                    if (currentPiece.shape[y][x]) {
                        const boardY = currentPiece.y + y;
                        const boardX = currentPiece.x + x;
                        if (boardY >= 0) {
                            board[boardY][boardX] = currentPiece.color;
                        }
                    }
                }
            }
        }
        
        // 清除完整的行
        function clearLines() {
            let linesCleared = 0;
            let clearedRows = [];

            for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
                if (board[y].every(cell => cell !== 0)) {
                    clearedRows.push(y);
                    board.splice(y, 1);
                    board.unshift(Array(BOARD_WIDTH).fill(0));
                    linesCleared++;
                    y++; // 重新检查这一行
                }
            }

            if (linesCleared > 0) {
                lines += linesCleared;
                // 计分系统：单行100分，双行300分，三行500分，四行800分
                const scoreMultiplier = [0, 100, 300, 500, 800];
                score += (scoreMultiplier[linesCleared] || linesCleared * 100) * level;
                level = Math.floor(lines / 10) + 1;
                dropInterval = Math.max(50, 1000 - (level - 1) * 80);
                updateDisplay();

                // 添加视觉反馈
                flashClearedLines(clearedRows);
            }
        }

        // 闪烁清除的行
        function flashClearedLines(rows) {
            // 播放音效（使用Web Audio API创建简单音效）
            playSound(rows.length);

            // 视觉反馈 - 短暂高亮清除的行
            setTimeout(() => {
                drawBoard();
            }, 100);
        }

        // 播放音效
        function playSound(linesCleared) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // 根据消除行数设置不同音调
                const frequencies = [0, 523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
                oscillator.frequency.setValueAtTime(frequencies[linesCleared] || 523.25, audioContext.currentTime);
                oscillator.type = 'square';

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                // 静默处理音频错误
            }
        }
        
        // 旋转方块
        function rotatePiece() {
            const rotated = currentPiece.shape[0].map((_, i) =>
                currentPiece.shape.map(row => row[i]).reverse()
            );
            
            if (!checkCollision(currentPiece, 0, 0, rotated)) {
                currentPiece.shape = rotated;
            }
        }
        
        // 移动方块
        function movePiece(dx, dy) {
            if (!checkCollision(currentPiece, dx, dy)) {
                currentPiece.x += dx;
                currentPiece.y += dy;
                return true;
            }
            return false;
        }
        
        // 更新显示
        function updateDisplay() {
            document.getElementById('score').textContent = score.toLocaleString();
            document.getElementById('level').textContent = level;
            document.getElementById('lines').textContent = lines;
            drawNextPiece();
        }
        
        // 绘制下一个方块
        function drawNextPiece() {
            nextCtx.clearRect(0, 0, nextCanvas.width, nextCanvas.height);
            if (nextPiece) {
                const offsetX = (nextCanvas.width - nextPiece.shape[0].length * 15) / 2;
                const offsetY = (nextCanvas.height - nextPiece.shape.length * 15) / 2;
                
                for (let y = 0; y < nextPiece.shape.length; y++) {
                    for (let x = 0; x < nextPiece.shape[y].length; x++) {
                        if (nextPiece.shape[y][x]) {
                            nextCtx.fillStyle = nextPiece.color;
                            nextCtx.fillRect(offsetX + x * 15, offsetY + y * 15, 15, 15);
                            nextCtx.strokeStyle = '#333';
                            nextCtx.strokeRect(offsetX + x * 15, offsetY + y * 15, 15, 15);
                        }
                    }
                }
            }
        }
        
        // 游戏结束
        function gameOver() {
            gameRunning = false;
            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameOver').style.display = 'block';
        }
        
        // 重新开始游戏
        function restartGame() {
            initBoard();
            score = 0;
            level = 1;
            lines = 0;
            dropInterval = 1000;
            gameRunning = true;
            isPaused = false;
            currentPiece = createPiece();
            nextPiece = createPiece();
            document.getElementById('gameOver').style.display = 'none';
            updateDisplay();
        }
        
        // 游戏主循环
        function gameLoop(timestamp) {
            if (!gameRunning) {
                return;
            }

            // 处理游戏手柄输入
            if (gamepadConnected && !isPaused) {
                handleGamepadInput();
            }

            if (isPaused) {
                // 显示暂停提示
                ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('游戏暂停', canvas.width / 2, canvas.height / 2);
                ctx.fillText('按空格或Start键继续', canvas.width / 2, canvas.height / 2 + 30);
                animationId = requestAnimationFrame(gameLoop);
                return;
            }

            if (timestamp - dropTime > dropInterval) {
                if (!movePiece(0, 1)) {
                    placePiece();
                    clearLines();
                    currentPiece = nextPiece;
                    nextPiece = createPiece();

                    if (checkCollision(currentPiece)) {
                        gameOver();
                        return;
                    }
                }
                dropTime = timestamp;
            }

            drawBoard();
            animationId = requestAnimationFrame(gameLoop);
        }
        
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) {
                if (e.key === 'r' || e.key === 'R') {
                    restartGame();
                }
                return;
            }

            if (e.key === ' ') {
                e.preventDefault();
                isPaused = !isPaused;
                return;
            }

            if (isPaused) return;

            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    movePiece(-1, 0);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    movePiece(1, 0);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    movePiece(0, 1);
                    score += 1; // 快速下降奖励分数
                    updateDisplay();
                    break;
                case ' ':
                    // 硬降 - 直接降到底部
                    if (e.ctrlKey) {
                        e.preventDefault();
                        hardDrop();
                    }
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    rotatePiece();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    restartGame();
                    break;
            }
        });
        
        // 游戏手柄事件监听
        window.addEventListener('gamepadconnected', (e) => {
            console.log('游戏手柄已连接:', e.gamepad.id);
            gamepadConnected = true;
            gamepadIndex = e.gamepad.index;
            showGamepadStatus('手柄已连接: ' + e.gamepad.id);
        });

        window.addEventListener('gamepaddisconnected', (e) => {
            console.log('游戏手柄已断开:', e.gamepad.id);
            gamepadConnected = false;
            gamepadIndex = -1;
            showGamepadStatus('手柄已断开');
        });

        // 显示手柄状态
        function showGamepadStatus(message) {
            // 更新界面上的手柄状态
            const statusElement = document.getElementById('gamepadStatus');
            if (statusElement) {
                statusElement.textContent = gamepadConnected ? '已连接' : '未连接';
                statusElement.style.color = gamepadConnected ? '#4caf50' : '#666';
            }

            // 创建临时提示
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1976d2;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                z-index: 1000;
                font-size: 14px;
            `;
            statusDiv.textContent = message;
            document.body.appendChild(statusDiv);

            setTimeout(() => {
                if (document.body.contains(statusDiv)) {
                    document.body.removeChild(statusDiv);
                }
            }, 3000);
        }

        // 游戏手柄输入处理
        function handleGamepadInput() {
            if (!gamepadConnected) return;

            const gamepads = navigator.getGamepads();
            const gamepad = gamepads[gamepadIndex];

            if (!gamepad) return;

            const currentTime = Date.now();

            // 防止按键重复触发，设置最小间隔
            if (currentTime - gamepadLastUpdate < 150) return;

            // 方向键控制
            if (gamepad.axes[0] < -0.5) { // 左
                if (!gamepadButtonStates.left) {
                    movePiece(-1, 0);
                    gamepadButtonStates.left = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.left = false;
            }

            if (gamepad.axes[0] > 0.5) { // 右
                if (!gamepadButtonStates.right) {
                    movePiece(1, 0);
                    gamepadButtonStates.right = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.right = false;
            }

            if (gamepad.axes[1] > 0.5) { // 下
                if (!gamepadButtonStates.down) {
                    movePiece(0, 1);
                    score += 1;
                    updateDisplay();
                    gamepadButtonStates.down = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.down = false;
            }

            if (gamepad.axes[1] < -0.5) { // 上（旋转）
                if (!gamepadButtonStates.up) {
                    rotatePiece();
                    gamepadButtonStates.up = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.up = false;
            }

            // 按钮控制
            if (gamepad.buttons[0].pressed) { // A键 - 旋转
                if (!gamepadButtonStates.buttonA) {
                    rotatePiece();
                    gamepadButtonStates.buttonA = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.buttonA = false;
            }

            if (gamepad.buttons[1].pressed) { // B键 - 硬降
                if (!gamepadButtonStates.buttonB) {
                    hardDrop();
                    gamepadButtonStates.buttonB = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.buttonB = false;
            }

            if (gamepad.buttons[9].pressed) { // Start键 - 暂停
                if (!gamepadButtonStates.start) {
                    isPaused = !isPaused;
                    gamepadButtonStates.start = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.start = false;
            }

            if (gamepad.buttons[8].pressed) { // Select键 - 重新开始
                if (!gamepadButtonStates.select) {
                    restartGame();
                    gamepadButtonStates.select = true;
                    gamepadLastUpdate = currentTime;
                }
            } else {
                gamepadButtonStates.select = false;
            }
        }

        // 检测已连接的手柄
        function checkExistingGamepads() {
            const gamepads = navigator.getGamepads();
            for (let i = 0; i < gamepads.length; i++) {
                if (gamepads[i]) {
                    gamepadConnected = true;
                    gamepadIndex = i;
                    showGamepadStatus('检测到手柄: ' + gamepads[i].id);
                    break;
                }
            }
        }

        // 初始化游戏
        initBoard();
        currentPiece = createPiece();
        nextPiece = createPiece();
        updateDisplay();

        // 检测已连接的手柄
        checkExistingGamepads();

        requestAnimationFrame(gameLoop);
    </script>
</body>
</html>
