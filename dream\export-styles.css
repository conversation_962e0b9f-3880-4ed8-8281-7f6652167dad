/* 导出底片按钮样式 */
#exportDreamBtn {
  background: linear-gradient(135deg, #34d399, #3b82f6);
  color: white;
  margin-top: 20px;
  text-transform: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

#exportDreamBtn:hover {
  background: linear-gradient(135deg, #10b981, #2563eb);
  transform: translateY(-2px);
}

#exportDreamBtn i {
  font-size: 16px;
}

/* 导出底片预览模态框 */
.dream-export-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.dream-export-modal.active {
  opacity: 1;
  visibility: visible;
}

.dream-export-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
  position: relative;
}

.dream-export-close {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  background: none;
  border: none;
  width: auto;
  padding: 5px;
  margin: 0;
  box-shadow: none;
}

.dream-export-image {
  width: 100%;
  border-radius: 4px;
  margin-bottom: 15px;
}

.dream-export-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.dream-export-actions button {
  width: 48%;
  margin-top: 0;
}

/* 本地手写字体定义 */
@font-face {
  font-family: 'LocalHandwritten';
  src: url('fonts/NiHeWoDeLangManYuZhou-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 备用字体 */
@font-face {
  font-family: 'FallbackHandwritten';
  src: local('KaiTi'), local('楷体'), local('STKaiti'), local('华文楷体');
  font-weight: normal;
  font-style: normal;
}

/* 普通字体 */
@font-face {
  font-family: 'NanoQyongDaSong C';
  src: url('fonts/NanoQyongDaSong C.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 手写字体样式 */
.handwritten-text {
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif;
  line-height: 1.8;
}

/* 选择手写风格时全局应用 */
.use-handwritten * {
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif !important;
}

/* 底片样式 */
.dream-film {
  background-color: #e6f2ff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 30px;
  position: relative;
}

.dream-film-header {
  text-align: center;
  margin-bottom: 20px;
}

.dream-film-title {
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive;
  font-size: 28px;
  color: #333;
  margin-bottom: 5px;
}

.dream-film-date {
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif;
  color: #666;
  font-size: 14px;
}

.dream-film-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dream-film-description {
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive;
  font-size: 18px;
  color: #333;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.dream-film-image-container {
  text-align: center;
}

.dream-film-image {
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.dream-film-analysis {
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif;
  font-size: 16px;
  color: #444;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.dream-film-footer {
  text-align: right;
  margin-top: 20px;
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif;
  font-size: 14px;
  color: #888;
}

/* 水印效果 */
.dream-film-watermark {
  position: absolute;
  bottom: 20px;
  right: 20px;
  opacity: 0.2;
  font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive;
  font-size: 24px;
  color: #333;
  transform: rotate(-15deg);
} 