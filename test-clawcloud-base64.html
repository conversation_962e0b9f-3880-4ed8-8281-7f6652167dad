<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试CLAWCLOUD Base64返回</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
        }
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .api-config {
            margin-bottom: 15px;
        }
        .api-config label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .api-config input, .api-config select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }
        .test-btn {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .test-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .endpoint {
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }
        .endpoint h4 {
            margin-bottom: 10px;
            color: #495057;
        }
        .status {
            padding: 8px 12px;
            border-radius: 5px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        .status.testing {
            background: #cce5ff;
            color: #004085;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.failed {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试CLAWCLOUD是否支持Base64图像返回</h1>
        
        <div class="test-section">
            <h3>🔧 API配置</h3>
            <div class="api-config">
                <label for="api-key">API密钥:</label>
                <input type="password" id="api-key" placeholder="输入你的API密钥" value="AIzaSyDZRfmpBIMHeG_r9lx4ftaSWnP_p985JtI">
            </div>
            <div class="api-config">
                <label for="test-prompt">测试提示词:</label>
                <input type="text" id="test-prompt" value="生成一朵简单的红色玫瑰花图片" placeholder="输入图像生成提示词">
            </div>
            <button class="test-btn" onclick="runComparison()">🚀 开始对比测试</button>
            <button class="test-btn" onclick="clearResults()">🧹 清除结果</button>
        </div>

        <div class="comparison">
            <div class="endpoint">
                <h4>🌐 Google原始API</h4>
                <div id="google-status" class="status pending">等待测试</div>
                <div id="google-result" class="result info">点击开始测试按钮...</div>
            </div>
            
            <div class="endpoint">
                <h4>☁️ CLAWCLOUD中转API</h4>
                <div id="clawcloud-status" class="status pending">等待测试</div>
                <div id="clawcloud-result" class="result info">点击开始测试按钮...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结论</h3>
            <div id="conclusion" class="result info">等待测试完成...</div>
        </div>
    </div>

    <script>
        const API_ENDPOINTS = {
            google: 'https://generativelanguage.googleapis.com/v1beta',
            clawcloud: 'https://iebemjnrzxnv.ap-southeast-1.clawcloudrun.com/V1'
        };

        async function testGoogleAPI(apiKey, prompt) {
            const url = `${API_ENDPOINTS.google}/models/gemini-2.0-flash-exp-image-generation:generateContent?key=${apiKey}`;
            
            const requestBody = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    responseModalities: ["Text", "Image"]
                }
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            return { response, requestBody };
        }

        async function testClawCloudAPI(apiKey, prompt) {
            const url = `${API_ENDPOINTS.clawcloud}/images/generations`;
            
            const requestBody = {
                model: "imagen-3.0-generate-002",
                prompt: prompt,
                n: 1,
                size: "1024x1024",
                response_format: "b64_json"
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify(requestBody)
            });

            return { response, requestBody };
        }

        async function runComparison() {
            const apiKey = document.getElementById('api-key').value.trim();
            const prompt = document.getElementById('test-prompt').value.trim();

            if (!apiKey || !prompt) {
                alert('请输入API密钥和测试提示词');
                return;
            }

            // 重置状态
            document.getElementById('google-status').className = 'status testing';
            document.getElementById('google-status').textContent = '正在测试...';
            document.getElementById('clawcloud-status').className = 'status testing';
            document.getElementById('clawcloud-status').textContent = '正在测试...';

            const results = {
                google: null,
                clawcloud: null
            };

            // 测试Google API
            try {
                console.log('🧪 测试Google原始API...');
                const { response, requestBody } = await testGoogleAPI(apiKey, prompt);
                const data = await response.json();
                
                results.google = {
                    success: response.ok,
                    status: response.status,
                    requestBody,
                    responseData: data,
                    hasBase64: checkForBase64(data),
                    error: response.ok ? null : data
                };

                updateGoogleResult(results.google);
                
            } catch (error) {
                results.google = {
                    success: false,
                    error: error.message
                };
                updateGoogleResult(results.google);
            }

            // 测试CLAWCLOUD API
            try {
                console.log('🧪 测试CLAWCLOUD中转API...');
                const { response, requestBody } = await testClawCloudAPI(apiKey, prompt);
                const data = await response.json();
                
                results.clawcloud = {
                    success: response.ok,
                    status: response.status,
                    requestBody,
                    responseData: data,
                    hasBase64: checkForBase64(data),
                    error: response.ok ? null : data
                };

                updateClawCloudResult(results.clawcloud);
                
            } catch (error) {
                results.clawcloud = {
                    success: false,
                    error: error.message
                };
                updateClawCloudResult(results.clawcloud);
            }

            // 显示结论
            showConclusion(results);
        }

        function checkForBase64(data) {
            const dataStr = JSON.stringify(data);
            
            // 检查Google格式的base64
            if (data.candidates && data.candidates[0]?.content?.parts) {
                for (const part of data.candidates[0].content.parts) {
                    if (part.inlineData && part.inlineData.data) {
                        return {
                            found: true,
                            format: 'Google格式',
                            size: part.inlineData.data.length,
                            mimeType: part.inlineData.mimeType
                        };
                    }
                }
            }
            
            // 检查OpenAI格式的base64
            if (data.data && Array.isArray(data.data)) {
                for (const item of data.data) {
                    if (item.b64_json) {
                        return {
                            found: true,
                            format: 'OpenAI格式',
                            size: item.b64_json.length
                        };
                    }
                }
            }
            
            // 检查是否包含base64字符串
            const base64Pattern = /[A-Za-z0-9+/]{100,}={0,2}/;
            if (base64Pattern.test(dataStr)) {
                return {
                    found: true,
                    format: '未知格式',
                    note: '检测到疑似base64数据'
                };
            }
            
            return { found: false };
        }

        function updateGoogleResult(result) {
            const statusEl = document.getElementById('google-status');
            const resultEl = document.getElementById('google-result');
            
            if (result.success) {
                statusEl.className = 'status success';
                statusEl.textContent = result.hasBase64.found ? '✅ 成功返回Base64' : '⚠️ 成功但无Base64';
                
                resultEl.className = 'result success';
                resultEl.textContent = `状态: ${result.status}
Base64检测: ${result.hasBase64.found ? '找到' : '未找到'}
${result.hasBase64.found ? `格式: ${result.hasBase64.format}
大小: ${result.hasBase64.size} 字符
MIME: ${result.hasBase64.mimeType || 'N/A'}` : ''}

请求体:
${JSON.stringify(result.requestBody, null, 2)}

响应数据:
${JSON.stringify(result.responseData, null, 2)}`;
            } else {
                statusEl.className = 'status failed';
                statusEl.textContent = '❌ 请求失败';
                
                resultEl.className = 'result error';
                resultEl.textContent = `错误: ${result.error}`;
            }
        }

        function updateClawCloudResult(result) {
            const statusEl = document.getElementById('clawcloud-status');
            const resultEl = document.getElementById('clawcloud-result');
            
            if (result.success) {
                statusEl.className = 'status success';
                statusEl.textContent = result.hasBase64.found ? '✅ 成功返回Base64' : '⚠️ 成功但无Base64';
                
                resultEl.className = 'result success';
                resultEl.textContent = `状态: ${result.status}
Base64检测: ${result.hasBase64.found ? '找到' : '未找到'}
${result.hasBase64.found ? `格式: ${result.hasBase64.format}
大小: ${result.hasBase64.size} 字符` : ''}

请求体:
${JSON.stringify(result.requestBody, null, 2)}

响应数据:
${JSON.stringify(result.responseData, null, 2)}`;
            } else {
                statusEl.className = 'status failed';
                statusEl.textContent = '❌ 请求失败';
                
                resultEl.className = 'result error';
                resultEl.textContent = `错误: ${result.error}`;
            }
        }

        function showConclusion(results) {
            const conclusionEl = document.getElementById('conclusion');
            
            let conclusion = '📊 测试结论:\n\n';
            
            if (results.google?.success && results.google.hasBase64.found) {
                conclusion += '✅ Google原始API: 成功返回Base64数据\n';
            } else {
                conclusion += '❌ Google原始API: 未能获取Base64数据\n';
            }
            
            if (results.clawcloud?.success && results.clawcloud.hasBase64.found) {
                conclusion += '✅ CLAWCLOUD中转API: 成功返回Base64数据\n';
                conclusion += '\n🎉 结论: CLAWCLOUD支持Base64返回，可以实现本地转换！';
            } else if (results.clawcloud?.success) {
                conclusion += '⚠️ CLAWCLOUD中转API: 请求成功但未返回Base64数据\n';
                conclusion += '\n🤔 结论: CLAWCLOUD可能不支持图像生成或Base64返回';
            } else {
                conclusion += '❌ CLAWCLOUD中转API: 请求失败\n';
                conclusion += '\n💡 建议: 使用Google原始API进行Base64转换';
            }
            
            conclusionEl.textContent = conclusion;
        }

        function clearResults() {
            document.getElementById('google-status').className = 'status pending';
            document.getElementById('google-status').textContent = '等待测试';
            document.getElementById('google-result').className = 'result info';
            document.getElementById('google-result').textContent = '点击开始测试按钮...';
            
            document.getElementById('clawcloud-status').className = 'status pending';
            document.getElementById('clawcloud-status').textContent = '等待测试';
            document.getElementById('clawcloud-result').className = 'result info';
            document.getElementById('clawcloud-result').textContent = '点击开始测试按钮...';
            
            document.getElementById('conclusion').textContent = '等待测试完成...';
        }
    </script>
</body>
</html>
