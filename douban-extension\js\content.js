// 监听来自弹出窗口的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === "getPageInfo") {
    // 获取当前页面的信息
    const pageInfo = extractPageInfo();
    sendResponse(pageInfo);
  }
  return true;
});

// 从当前页面提取信息
function extractPageInfo() {
  const url = window.location.href;
  let itemId = null;
  let itemType = null;
  let title = '';
  
  // 电影页面
  const movieMatch = url.match(/movie\.douban\.com\/subject\/(\d+)/);
  if (movieMatch) {
    itemId = movieMatch[1];
    itemType = 'movie';
    
    // 获取标题（如果有）
    const titleElement = document.querySelector('h1 span[property="v:itemreviewed"]');
    if (titleElement) {
      title = titleElement.textContent.trim();
    } else {
      title = document.title.replace(' (豆瓣)', '').trim();
    }
    
    // 不再添加浮动按钮
    return { itemId, itemType, title, url };
  }
  
  // 图书页面
  const bookMatch = url.match(/book\.douban\.com\/subject\/(\d+)/);
  if (bookMatch) {
    itemId = bookMatch[1];
    itemType = 'book';
    
    // 获取标题（如果有）
    const titleElement = document.querySelector('h1 span[property="v:itemreviewed"]');
    if (titleElement) {
      title = titleElement.textContent.trim();
    } else {
      title = document.title.replace(' (豆瓣)', '').trim();
    }
    
    // 不再添加浮动按钮
    return { itemId, itemType, title, url };
  }
  
  return { itemId: null, itemType: null, title: '', url };
}

// 我们保留提取功能，但不添加浮动按钮，这样依然能响应getPageInfo请求
console.log('豆瓣评论分析器已加载，可通过扩展图标使用'); 