// 处理扩展图标点击 - 带详细调试信息
chrome.action.onClicked.addListener(async (tab) => {
  console.log('扩展图标被点击，当前标签页:', tab.id);

  // 检查Chrome版本和API支持
  if (!chrome.sidePanel) {
    console.error('❌ Chrome版本太低，不支持侧边栏API (需要Chrome 114+)');
    alert('您的Chrome版本不支持侧边栏功能，需要Chrome 114+版本');
    return;
  }

  if (!chrome.sidePanel.open) {
    console.error('❌ sidePanel.open API不可用');
    alert('侧边栏API不可用，请检查Chrome版本');
    return;
  }

  try {
    console.log('🔄 尝试打开侧边栏...');
    await chrome.sidePanel.open({ tabId: tab.id });
    console.log('✅ 侧边栏已成功打开');
  } catch (error) {
    console.error('❌ 打开侧边栏失败:', error);
    console.error('错误详情:', error.message);

    // 备用方案：打开独立窗口
    console.log('🔄 使用备用方案：打开独立窗口');
    try {
      await chrome.windows.create({
        url: 'popup.html',
        type: 'popup',
        width: 400,
        height: 700,
        left: screen.availWidth - 420,
        top: 100
      });
      console.log('✅ 独立窗口已打开');
    } catch (windowError) {
      console.error('❌ 打开独立窗口也失败:', windowError);
    }
  }
});

// 安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('🚀 AI便利贴扩展已安装 - 版本更新');

  // 检查API支持情况
  if (chrome.sidePanel) {
    console.log('✅ 侧边栏API可用');
  } else {
    console.log('❌ 侧边栏API不可用 - Chrome版本可能太低');
  }
});

// 启动时也输出日志
console.log('🔄 Background script 已启动');
