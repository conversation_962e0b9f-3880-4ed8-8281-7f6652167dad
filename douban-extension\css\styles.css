:root {
  --primary-color: #00B51D;
  --primary-hover: #00A11B;
  --text-color: #2C3E50;
  --light-bg: #F0F4F8;
  --card-bg: #FFFFFF;
  --border-color: #E1E8ED;
  --placeholder-color: #A0AEC0;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.5;
  min-width: 300px;
  width: 100%;
  min-height: 600px;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.container {
  width: 100%;
  max-width: 100%;
  min-width: 300px;
  padding: 15px;
  background-color: white;
  border-radius: 0;
  box-shadow: none;
  min-height: 100vh;

  /* 弹窗模式样式 */
  @media (max-width: 400px) {
    width: 380px;
    min-height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

.header {
  margin-bottom: 15px;
  text-align: center;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.logo-icon {
  color: #37a;
  font-size: 24px;
}

h1 {
  font-size: 20px;
  color: #37a;
  font-weight: 600;
}

h2 {
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 500;
  color: #494949;
}

h3 {
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #494949;
}

.input-section {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 6px;
}

.input-group {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.input-group label {
  width: 60px;
  font-weight: 500;
}

input, select {
  flex: 1;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

input:focus, select:focus {
  outline: none;
  border-color: #37a;
}

.current-page-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
}

#current-url {
  color: #666;
  font-style: italic;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#current-title {
  color: #37a;
  font-style: normal;
  font-weight: 500;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
}

button {
  cursor: pointer;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s;
}

.primary-button {
  width: 100%;
  padding: 10px;
  background-color: #37a;
  color: white;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.primary-button:hover {
  background-color: #2e8bc0;
}

.primary-button:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.secondary-button {
  padding: 6px 12px;
  background-color: #f0f0f0;
  color: #555;
  font-size: 13px;
}

.secondary-button:hover {
  background-color: #e0e0e0;
}

.icon-button {
  background: none;
  color: #666;
  font-size: 18px;
}

.icon-button:hover {
  color: #37a;
}

.result-section {
  margin-bottom: 15px;
}

.result-box {
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 6px;
  font-size: 14px;
  position: relative;
}

.copy-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: transparent;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  z-index: 5;
}

.copy-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #37a;
}

.copy-button i {
  font-size: 16px;
}

.copy-icon {
  position: relative;
  width: 15px;
  height: 15px;
}

.copy-icon::before,
.copy-icon::after {
  content: "";
  position: absolute;
  background-color: currentColor;
  border: 1px solid currentColor;
  border-radius: 2px;
}

.copy-icon::before {
  width: 10px;
  height: 12px;
  top: 0;
  right: 0;
}

.copy-icon::after {
  width: 10px;
  height: 12px;
  bottom: 0;
  left: 0;
  opacity: 0.7;
}

.placeholder {
  color: #aaa;
  text-align: center;
  font-style: italic;
}

.analysis-result {
  white-space: pre-line;
  color: #333;
  line-height: 1.6;
}

.analysis-result h1, 
.analysis-result h2 {
  margin-top: 12px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.analysis-result h1 {
  font-size: 18px;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.analysis-result h2 {
  font-size: 16px;
}

.analysis-result h3 {
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 6px;
  font-weight: 600;
}

.analysis-result p {
  margin-bottom: 8px;
}

.analysis-result ul, 
.analysis-result ol {
  margin-left: 20px;
  margin-bottom: 8px;
}

.analysis-result li {
  margin-bottom: 4px;
}

.analysis-result strong {
  font-weight: 600;
}

.analysis-result em {
  font-style: italic;
}

.analysis-result blockquote {
  border-left: 3px solid #ddd;
  padding-left: 10px;
  color: #666;
  margin: 8px 0;
}

.analysis-result code {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 12px;
}

.copy-success {
  position: absolute;
  top: 40px;
  right: 10px;
  background-color: #37a;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
}

.copy-success.show {
  opacity: 1;
}

.error {
  color: #e53e3e;
}

.settings-section {
  text-align: right;
  position: relative;
}

#settings-panel {
  position: absolute;
  right: 0;
  bottom: 30px;
  width: 100%;
  padding: 15px;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 6px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  text-align: left;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 8px;
  flex: 0 0 auto;
  width: auto;
}

.api-info {
  margin: 10px 0;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-size: 13px;
}

.status {
  margin: 8px 0;
  font-size: 13px;
  color: #666;
}

.quota-info {
  margin-top: 10px;
  font-size: 12px;
  color: #888;
}

.hidden {
  display: none;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.note-text {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
  font-style: italic;
}

.custom-prompt-section {
  margin-bottom: 12px;
  position: relative;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #333;
}

#custom-prompt {
  width: 100%;
  height: 60px;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  resize: vertical;
  font-family: inherit;
}

#custom-prompt:focus {
  outline: none;
  border-color: #37a;
}

.tooltip {
  position: relative;
  display: inline-block;
  margin-top: 5px;
  color: #666;
  font-size: 14px;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 300px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 6px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  line-height: 1.4;
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.comments-pages-section {
  margin-bottom: 12px;
}

.pages-slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pages-slider {
  flex-grow: 1;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: #ddd;
  outline: none;
  border-radius: 3px;
}

.pages-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #37a;
  cursor: pointer;
}

.pages-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #37a;
  cursor: pointer;
  border: none;
}

#pages-display {
  min-width: 40px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #37a;
}

.comments-stats {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f0f7ff;
  border-left: 3px solid #37a;
  border-radius: 3px;
  font-size: 13px;
  color: #555;
}

.note-info {
  margin-top: 4px;
  font-size: 12px;
  color: #dd6161;
  font-style: italic;
}

.model-selection {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.model-selection label {
  width: 80px;
  font-weight: 500;
  margin-right: 10px;
}

.model-selection select {
  flex: 1;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.custom-model-input {
  margin-bottom: 12px;
  padding-left: 80px;
}

.custom-model-input input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* 导出选项区域 */
.export-options {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.export-success {
    color: #00B51D;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.export-success.show {
    opacity: 1;
} 