<!DOCTYPE html>
<html>
<head>
  <title>画梦录 - 字体加载测试工具</title>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #3b82f6;
      text-align: center;
    }
    
    .section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .section-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .font-test {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .font-name {
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .sample {
      font-size: 24px;
      line-height: 1.5;
    }
    
    .status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
    }
    
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    
    .warning {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    pre {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 14px;
    }
    
    button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-right: 10px;
    }
    
    button:hover {
      background-color: #2563eb;
    }
    
    /* 测试本地手写字体 */
    @font-face {
      font-family: 'LocalHandwritten';
      src: url('./fonts/NiHeWoDeLangManYuZhou-2.ttf') format('truetype');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
    
    /* 备用字体 */
    @font-face {
      font-family: 'FallbackHandwritten';
      src: local('KaiTi'), local('楷体'), local('STKaiti'), local('华文楷体');
      font-weight: normal;
      font-style: normal;
    }
    
    .handwritten-text {
      font-family: 'LocalHandwritten', 'FallbackHandwritten', cursive, sans-serif;
      line-height: 1.8;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>画梦录 - 字体加载测试工具</h1>
    
    <div class="section">
      <div class="section-title">字体加载状态</div>
      <div id="fontLoadingStatus" class="status">检测中...</div>
    </div>
    
    <div class="font-test">
      <div class="font-name">本地手写字体测试</div>
      <div class="sample handwritten-text">梦境记录：我梦见自己在一片广阔的森林中漫步，阳光透过树叶洒落在地面上，形成斑驳的光影。</div>
      <div id="localFontStatus" class="status">检测中...</div>
    </div>
    
    <div class="font-test">
      <div class="font-name">系统默认字体测试</div>
      <div class="sample">梦境记录：我梦见自己在一片广阔的森林中漫步，阳光透过树叶洒落在地面上，形成斑驳的光影。</div>
    </div>
    
    <div class="section">
      <div class="section-title">字体文件检测</div>
      <div id="fontFileStatus" class="status">检测中...</div>
      <div id="fontFileDetails"></div>
    </div>
    
    <div class="section">
      <div class="section-title">调试信息</div>
      <pre id="debugInfo"></pre>
    </div>
    
    <div class="section">
      <div class="section-title">操作</div>
      <button id="reloadBtn">重新加载页面</button>
      <button id="clearCacheBtn">清除缓存并重新加载</button>
      <button id="checkFontBtn">重新检测字体</button>
    </div>
    
    <div id="instructions" class="section">
      <div class="section-title">使用说明</div>
      <p>此工具用于诊断"画梦录"插件中手写字体加载问题。</p>
      <p>如果看到"字体加载成功"的绿色提示，说明字体已正确加载。</p>
      <p>如果看到错误提示，请按照下方建议进行操作。</p>
    </div>
  </div>
  
  <script>
    // 调试信息记录
    function log(message) {
      const debugInfo = document.getElementById('debugInfo');
      const timestamp = new Date().toLocaleTimeString();
      debugInfo.textContent += `[${timestamp}] ${message}\n`;
      console.log(message);
    }
    
    // 检测字体加载状态
    function checkFontLoading() {
      log('开始检测字体加载状态...');
      
      const fontLoadingStatus = document.getElementById('fontLoadingStatus');
      const localFontStatus = document.getElementById('localFontStatus');
      const instructions = document.getElementById('instructions');
      
      // 创建字体检测元素
      const fontDetector = document.createElement('span');
      fontDetector.style.fontFamily = 'LocalHandwritten';
      fontDetector.style.visibility = 'hidden';
      fontDetector.style.position = 'absolute';
      fontDetector.textContent = 'test';
      document.body.appendChild(fontDetector);
      
      // 获取计算后的字体
      const computedStyle = window.getComputedStyle(fontDetector);
      const loadedFont = computedStyle.fontFamily;
      
      log(`计算后的字体: ${loadedFont}`);
      
      // 检测字体是否加载成功
      if (loadedFont.indexOf('LocalHandwritten') !== -1) {
        fontLoadingStatus.textContent = '✓ 字体加载成功！';
        fontLoadingStatus.className = 'status success';
        localFontStatus.textContent = '✓ 本地手写字体加载成功！';
        localFontStatus.className = 'status success';
        
        instructions.innerHTML = `
          <div class="section-title">使用说明</div>
          <div class="status success">
            <h3>恭喜！字体已正确加载</h3>
            <p>您现在可以在"画梦录"插件中使用手写字体功能了。请确保在设置中选择"手写风格"选项。</p>
          </div>
        `;
      } else {
        fontLoadingStatus.textContent = '✗ 字体加载失败';
        fontLoadingStatus.className = 'status error';
        localFontStatus.textContent = '✗ 本地手写字体未能加载，可能未正确安装';
        localFontStatus.className = 'status error';
        
        instructions.innerHTML = `
          <div class="section-title">使用说明</div>
          <div class="status warning">
            <h3>字体未正确加载</h3>
            <p>请尝试以下解决方法：</p>
            <ol>
              <li>确认您已下载手写字体文件（.ttf或.otf格式）</li>
              <li>确保字体文件位于<code>dream/fonts/</code>目录中</li>
              <li>确保字体文件名为<code>NiHeWoDeLangManYuZhou-2.ttf</code></li>
              <li>尝试清除浏览器缓存并重新加载页面</li>
              <li>重启浏览器后再次尝试</li>
            </ol>
          </div>
        `;
      }
      
      // 清理检测元素
      document.body.removeChild(fontDetector);
      
      // 检测字体文件
      checkFontFile();
    }
    
    // 检测字体文件
    function checkFontFile() {
      log('开始检测字体文件...');
      
      const fontFileStatus = document.getElementById('fontFileStatus');
      const fontFileDetails = document.getElementById('fontFileDetails');
      
      // 尝试加载字体文件
      fetch('./fonts/NiHeWoDeLangManYuZhou-2.ttf')
        .then(response => {
          if (response.ok) {
            log('字体文件存在且可访问');
            fontFileStatus.textContent = '✓ 字体文件存在且可访问';
            fontFileStatus.className = 'status success';
            fontFileDetails.innerHTML = `
              <p>字体文件: <code>./fonts/NiHeWoDeLangManYuZhou-2.ttf</code></p>
              <p>状态码: ${response.status}</p>
              <p>文件类型: ${response.headers.get('content-type') || '未知'}</p>
            `;
            return response.blob();
          } else {
            throw new Error(`字体文件请求失败: ${response.status} ${response.statusText}`);
          }
        })
        .then(blob => {
          log(`字体文件大小: ${(blob.size / 1024).toFixed(2)} KB`);
          fontFileDetails.innerHTML += `<p>文件大小: ${(blob.size / 1024).toFixed(2)} KB</p>`;
        })
        .catch(error => {
          log(`字体文件检测错误: ${error.message}`);
          fontFileStatus.textContent = `✗ 字体文件检测失败: ${error.message}`;
          fontFileStatus.className = 'status error';
          fontFileDetails.innerHTML = `
            <p>请确认以下几点:</p>
            <ul>
              <li>字体文件 <code>NiHeWoDeLangManYuZhou-2.ttf</code> 是否存在于 <code>dream/fonts/</code> 目录中</li>
              <li>文件名是否正确（区分大小写）</li>
              <li>文件是否可读</li>
            </ul>
          `;
        });
    }
    
    // 初始化
    window.addEventListener('load', function() {
      log('页面加载完成，开始初始化...');
      
      // 检测字体加载
      setTimeout(checkFontLoading, 500);
      
      // 绑定按钮事件
      document.getElementById('reloadBtn').addEventListener('click', function() {
        log('用户点击了重新加载按钮');
        window.location.reload();
      });
      
      document.getElementById('clearCacheBtn').addEventListener('click', function() {
        log('用户点击了清除缓存按钮');
        // 添加随机参数避免缓存
        window.location.href = window.location.pathname + '?nocache=' + new Date().getTime();
      });
      
      document.getElementById('checkFontBtn').addEventListener('click', function() {
        log('用户点击了重新检测字体按钮');
        checkFontLoading();
      });
      
      // 记录浏览器信息
      log(`浏览器: ${navigator.userAgent}`);
    });
  </script>
</body>
</html> 