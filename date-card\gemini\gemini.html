<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>花卉艺术生成器</title>
  <link rel="stylesheet" href="../css/style.css">
  <link rel="stylesheet" href="gemini.css">
</head>
<body>
  <div class="container">
    <h1>花卉艺术生成器</h1>
    
    <div class="api-key-section">
      <label for="api-key">Gemini API密钥:</label>
      <input type="password" id="api-key" placeholder="输入您的API密钥">
      <button id="save-api-key">保存</button>
      <div id="api-key-status"></div>
    </div>

    <div class="mode-selection">
      <button id="text-to-image-mode" class="mode-button active">文本生成图像</button>
      <button id="image-edit-mode" class="mode-button">上传图片修改</button>
    </div>

    <div id="text-to-image-section">
      <div class="prompt-section">
        <label for="prompt">图像提示词:</label>
        <textarea id="prompt" placeholder="描述您想要生成的花卉艺术图像，例如：一朵盛开的牡丹，工笔画风格"></textarea>
      </div>

      <div class="button-section">
        <button id="generate-button">生成图像</button>
      </div>
    </div>

    <div id="image-upload-section" style="display: none;">
      <div class="upload-section">
        <label for="image-upload">上传图片:</label>
        <input type="file" id="image-upload" accept="image/*">
        <div class="upload-preview">
          <img id="upload-preview-image" style="display: none;">
        </div>
      </div>

      <div class="prompt-section">
        <label for="edit-upload-prompt">修改提示词:</label>
        <textarea id="edit-upload-prompt" placeholder="描述如何修改上传的图像"></textarea>
      </div>

      <div class="button-section">
        <button id="edit-upload-button" disabled>修改上传图像</button>
      </div>
    </div>

    <div class="loading-section" id="loading" style="display: none;">
      <div class="spinner"></div>
      <p>正在生成图像，请稍候...</p>
    </div>

    <div class="result-section">
      <div id="text-result"></div>
      <div class="image-container">
        <img id="generated-image" style="display: none;">
      </div>

      <div class="image-actions" style="display: none;" id="image-actions">
        <button id="save-image">保存图像</button>
        <button id="use-in-calendar">用于日历</button>
        <button id="edit-image">修改图像</button>
      </div>

      <div class="edit-section" style="display: none;" id="edit-section">
        <label for="edit-prompt">修改提示词:</label>
        <textarea id="edit-prompt" placeholder="描述如何修改当前图像"></textarea>
        <button id="apply-edit">应用修改</button>
      </div>
    </div>
  </div>

  <script src="gemini.js"></script>
</body>
</html> 