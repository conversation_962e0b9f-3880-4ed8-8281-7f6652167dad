// Base64转本地图片测试脚本
let currentImageBlob = null;
let currentImageUrl = null;

// DOM元素
const generateBtn = document.getElementById('generate-btn');
const downloadBtn = document.getElementById('download-btn');
const saveLocalBtn = document.getElementById('save-local-btn');
const statusDiv = document.getElementById('status');
const imageContainer = document.getElementById('image-container');
const techInfo = document.getElementById('tech-info');
const apiKeyInput = document.getElementById('api-key');
const flowerTypeSelect = document.getElementById('flower-type');
const artStyleSelect = document.getElementById('art-style');

// 事件监听器
generateBtn.addEventListener('click', generateImage);
downloadBtn.addEventListener('click', downloadImage);
saveLocalBtn.addEventListener('click', saveToLocal);

// 更新状态显示
function updateStatus(message, type = 'info') {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
}

// 生成AI图片
async function generateImage() {
    const apiKey = apiKeyInput.value.trim();
    if (!apiKey) {
        updateStatus('请输入API密钥', 'error');
        return;
    }

    const flowerType = flowerTypeSelect.value;
    const artStyle = artStyleSelect.value;
    
    // 禁用按钮
    generateBtn.disabled = true;
    updateStatus('正在生成AI图片...', 'loading');
    
    const startTime = Date.now();
    
    try {
        // 构建提示词
        const prompt = `请创作一幅${artStyle}的${flowerType}艺术画：
1. 画面主题：一朵盛开的${flowerType}
2. 艺术风格：${artStyle}
3. 要求：构图优美，意境深远，色彩丰富
4. 画面氛围：清新自然，富有生命力
5. 适合作为艺术收藏`;

        // 调用Gemini API
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    responseModalities: ["Text", "Image"]
                }
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API请求失败: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('API响应:', data);

        // 处理响应数据
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const parts = data.candidates[0].content.parts;
            
            for (const part of parts) {
                if (part.inlineData && part.inlineData.mimeType && part.inlineData.mimeType.startsWith('image/')) {
                    const base64Data = part.inlineData.data;
                    const mimeType = part.inlineData.mimeType;
                    
                    // 🎯 核心功能：Base64转本地Blob URL
                    const result = await convertBase64ToLocal(base64Data, mimeType, flowerType);
                    
                    if (result.success) {
                        const endTime = Date.now();
                        const duration = endTime - startTime;
                        
                        // 显示图片
                        displayImage(result.url, result.blob);
                        
                        // 显示技术信息
                        showTechInfo(base64Data, mimeType, result.blob, duration);
                        
                        updateStatus('✅ 图片生成成功！已转换为本地URL', 'success');
                        
                        // 启用下载按钮
                        downloadBtn.disabled = false;
                        saveLocalBtn.disabled = false;
                        
                        return;
                    }
                }
            }
        }
        
        throw new Error('未找到图片数据');
        
    } catch (error) {
        console.error('生成图片失败:', error);
        updateStatus(`❌ 生成失败: ${error.message}`, 'error');
        
        // 显示错误信息
        techInfo.innerHTML = `
            <p><strong>❌ 生成失败</strong></p>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>可能原因:</strong></p>
            <ul>
                <li>API密钥无效或过期</li>
                <li>网络连接问题</li>
                <li>API配额超限</li>
                <li>请求格式错误</li>
            </ul>
            <p><strong>解决方案:</strong></p>
            <ul>
                <li>检查API密钥是否正确</li>
                <li>确认网络连接正常</li>
                <li>等待一段时间后重试</li>
            </ul>
        `;
    } finally {
        generateBtn.disabled = false;
    }
}

// 🎯 核心功能：将Base64转换为本地Blob URL
async function convertBase64ToLocal(base64Data, mimeType, fileName) {
    try {
        console.log('🔄 开始转换Base64到本地URL...');
        
        // 1. 解码Base64数据
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        
        // 2. 创建Uint8Array
        const byteArray = new Uint8Array(byteNumbers);
        
        // 3. 创建Blob对象
        const blob = new Blob([byteArray], { type: mimeType });
        
        // 4. 创建本地URL
        const url = URL.createObjectURL(blob);
        
        // 5. 保存引用
        currentImageBlob = blob;
        currentImageUrl = url;
        
        console.log('✅ Base64转换成功!');
        console.log('Blob大小:', blob.size, 'bytes');
        console.log('MIME类型:', blob.type);
        console.log('本地URL:', url);
        
        return {
            success: true,
            url: url,
            blob: blob,
            size: blob.size,
            type: blob.type
        };
        
    } catch (error) {
        console.error('❌ Base64转换失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 显示图片
function displayImage(url, blob) {
    imageContainer.innerHTML = `
        <img src="${url}" alt="AI生成的花卉图片" style="max-width: 100%; max-height: 300px; border-radius: 8px;">
    `;
    
    console.log('🖼️ 图片显示成功');
    console.log('显示URL:', url);
    console.log('Blob信息:', blob);
}

// 显示技术信息
function showTechInfo(base64Data, mimeType, blob, duration) {
    const base64Size = base64Data.length;
    const blobSize = blob.size;
    const compressionRatio = ((base64Size - blobSize) / base64Size * 100).toFixed(2);
    
    techInfo.innerHTML = `
        <p><strong>✅ 转换成功!</strong></p>
        <hr style="margin: 10px 0;">
        
        <p><strong>📊 数据统计:</strong></p>
        <ul>
            <li>Base64大小: ${(base64Size / 1024).toFixed(2)} KB</li>
            <li>Blob大小: ${(blobSize / 1024).toFixed(2)} KB</li>
            <li>压缩率: ${compressionRatio}%</li>
            <li>处理耗时: ${duration}ms</li>
        </ul>
        
        <p><strong>🔧 技术详情:</strong></p>
        <ul>
            <li>MIME类型: ${mimeType}</li>
            <li>Blob类型: ${blob.type}</li>
            <li>URL类型: blob://</li>
            <li>存储位置: 浏览器内存</li>
        </ul>
        
        <p><strong>💡 优势:</strong></p>
        <ul>
            <li>✅ 无需云存储费用</li>
            <li>✅ 即时可用</li>
            <li>✅ 隐私保护</li>
            <li>✅ 支持离线访问</li>
        </ul>
    `;
}

// 下载图片
function downloadImage() {
    if (!currentImageBlob) {
        updateStatus('没有可下载的图片', 'error');
        return;
    }
    
    const flowerType = flowerTypeSelect.value;
    const artStyle = artStyleSelect.value;
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const fileName = `AI_${artStyle}_${flowerType}_${timestamp}.png`;
    
    // 创建下载链接
    const downloadLink = document.createElement('a');
    downloadLink.href = currentImageUrl;
    downloadLink.download = fileName;
    downloadLink.style.display = 'none';
    
    // 触发下载
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
    
    updateStatus(`📥 图片已下载: ${fileName}`, 'success');
    console.log('📥 图片下载完成:', fileName);
}

// 保存到本地存储
function saveToLocal() {
    if (!currentImageBlob) {
        updateStatus('没有可保存的图片', 'error');
        return;
    }
    
    try {
        // 使用FileReader读取Blob
        const reader = new FileReader();
        reader.onload = function(e) {
            const base64String = e.target.result;
            
            // 保存到localStorage
            const flowerType = flowerTypeSelect.value;
            const artStyle = artStyleSelect.value;
            const timestamp = Date.now();
            const key = `ai_image_${timestamp}`;
            
            const imageData = {
                base64: base64String,
                flowerType: flowerType,
                artStyle: artStyle,
                timestamp: timestamp,
                size: currentImageBlob.size,
                type: currentImageBlob.type
            };
            
            localStorage.setItem(key, JSON.stringify(imageData));
            updateStatus(`💾 图片已保存到本地存储: ${key}`, 'success');
            console.log('💾 图片保存到localStorage:', key);
        };
        
        reader.readAsDataURL(currentImageBlob);
        
    } catch (error) {
        console.error('保存失败:', error);
        updateStatus(`❌ 保存失败: ${error.message}`, 'error');
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Base64转本地图片测试页面已加载');
    updateStatus('准备就绪，请配置API密钥后开始测试', 'info');
    
    // 检查localStorage中的历史图片
    const savedImages = [];
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.startsWith('ai_image_')) {
            savedImages.push(key);
        }
    }
    
    if (savedImages.length > 0) {
        console.log(`📚 发现 ${savedImages.length} 张历史保存的图片`);
    }
});

// 清理函数：释放Blob URL
window.addEventListener('beforeunload', function() {
    if (currentImageUrl) {
        URL.revokeObjectURL(currentImageUrl);
        console.log('🧹 已清理Blob URL');
    }
});
